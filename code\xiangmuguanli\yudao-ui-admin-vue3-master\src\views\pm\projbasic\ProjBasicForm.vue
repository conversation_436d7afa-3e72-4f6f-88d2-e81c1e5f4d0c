<template>
  <!-- <div style="padding-top:0px;padding-bottom:20px;background-color: #ffffff;border-radius: 4px;"> -->
  <div>
    <el-row>
              <el-col :span="5" >
          <el-card shadow="never" class="compact-steps-card" style="height: calc(100vh - 160px);">
            <!-- 简洁的头部 -->
            <div class="compact-step-header">
              <div class="step-progress-bar">
                <div class="progress-fill" :style="{ width: Math.round(((active + 1) / 11) * 100) + '%' }"></div>
              </div>
              <div class="step-info-compact">
                <span class="step-count">{{ active + 1 }} / 11</span>
                <span class="step-percent">{{ Math.round(((active + 1) / 11) * 100) }}%</span>
              </div>
            </div>
            
                        <!-- 现代卡片式步骤列表 -->
            <div class="modern-steps-container">
              <div 
                v-for="(step, index) in stepList" 
                :key="index" 
                class="modern-step-item" 
                :class="{ 
                  'active': active === index, 
                  'completed': active > index,
                  'clickable': index <= active 
                }"
                @click="stepClick(index)"
              >
                <div class="step-number">
                  <span v-if="active <= index">{{ index + 1 }}</span>
                  <i v-else class="checkmark">✓</i>
                </div>
                <div class="step-content">
                  <div class="step-title">{{ step.title }}</div>
                  <div v-if="active === index" class="step-status">正在进行</div>
                  <div v-else-if="active > index" class="step-status completed">已完成</div>
                  <div v-else class="step-status">待进行</div>
                </div>
              </div>
            </div>
        </el-card>
      </el-col>
      <el-col :span="18" class="ml_20">
        <div class="first_1">
          <el-card v-if="active === 0" class="fir_card1" shadow="never">
            <!-- 替换原有第一步内容为新组件 -->
            <ProjBasicInfoForm
              ref="projBasicInfoFormRef"
              :initial-data="ruleForm"
              :id="statementId"
              :options="options"
              :ttttvData="ttttvData"
              :rules="rules"
              :otherUserArr1="otherUserArr"
              @refresh-other-orgs="getOtherOrgData"
            />
          </el-card>
          <el-card v-if="active===1" class="card_stl" shadow="always" >
            <el-divider content-position="left"><span class="size20">*</span> <span class="size18">一、项目简介</span></el-divider>
            <span style="color:#F56C6C">注：概述本科技成果的创新概念及研究基础，通过科技创新所形成的核心技术及拟解决的关键问题，应用场景，对经济社会发展的推动作用，项目技术资金需求等（限500字以内）。</span>
            <div style="width:100%;height: calc(100vh - 160px);margin-top:10px;margin-bottom:10px;overflow: auto;">
              <el-form ref="ruleFormRef2"  class="demo-ruleForm" style="max-width: 100%;height: calc(100vh - 160px)" :model="ruleForm2" :rules="rules2" label-width="auto" :size="formSize" status-icon>
                <el-form-item  prop='projOverview'>
                  <el-input class="inp_area" v-model="ruleForm2.projOverview" :rows="17" maxlength="500" style="width: 100%;font-size: 16px;" :placeholder="placeholderMsg" show-word-limit type="textarea" />
                </el-form-item>
              </el-form>
            </div>
          </el-card>
          <el-card v-if="active===2" class="card_stl" shadow="always" >
            <el-divider content-position="left"><span class="size20">*</span> <span class="size18">二、项目基本情况介绍</span></el-divider>
            <span style="color:#F56C6C">注：概述本科技成果创新点及核心竞争力；现有技术基础、知识产权背景；拟进一步解决的关键科学技术问题；拟解决的产业难点与痛点及其可能产生的市场影响（限2000字以内）。</span>
            <div style="width:100%;height: calc(100vh - 300px);margin-top:10px;margin-bottom:10px;overflow: auto;">
              <el-form ref="ruleFormRef3" style="width: 100%;" :model="ruleForm3" :rules="rules3" label-width="auto"  class="demo-ruleForm"  :size="formSize" status-icon>
                <div style="overflow:auto;">
                  <el-form-item  prop="projIntroductions"  class="label-befor">
                    <Editor v-model="ruleForm3.projIntroductions" style="width:100%;" ref="editorRef" :defaultContent="defaultContent" @change="change" />
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </el-card>
          <el-card v-if="active===3" class="card_stl" shadow="always">
            <el-divider content-position="left"><span class="size20">*</span> <span class="size18">三、项目预期目标与产业化规划</span></el-divider>
            <span style="color:#F56C6C">注：描述本项目科技成果实施概念验证后的预期目标与产业化规划，主要突出以下五项内容：未来成果转化地域和方式；未来形成的产品和目标客户；市场需求及规模，及本项目形成的产品将占有的市场空间；结合未来 3-5 年市场前景预测，描述未来产业规划，包括如何调整技术/产品/服务来迅速占领、拓展市场；根据项目概念验证的时间安排和关键节点，描述未来两年拟完成的产业化规划与目标、关键节点/里程碑（限2000字以内）。</span>
            <div style="width:100%;height: calc(100vh - 290px);margin-top:10px;margin-bottom:10px;overflow: auto;">
              <el-form ref="ruleFormRef4" style="max-width: 100%;" :model="ruleForm4" :rules="rules4" label-width="auto"  class="demo-ruleForm" :size="formSize" status-icon>
                <div style="overflow:auto">
                  <el-form-item  prop="projGoalsPlan"  class="label-befor">
                    <Editor v-model="ruleForm4.projGoalsPlan" ref="editorRef" style="width:100%;"  :defaultContent="defaultContent" @change="change" />
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </el-card>
          <el-card v-if="active===4" class="card_stl" shadow="always">
            <el-divider content-position="left"><span class="size20">*</span> <span class="size18">四、项目成熟度自我评判</span></el-divider>
            <el-tooltip placement="top"  content='"点击查看附件"国家工信部颁布的《技术成熟度等级划分及定义》""'>
              <span style="color:#F56C6C" @click="annexEvaluationImage = true">注：根据国家工信部颁布的《技术成熟度等级划分及定义》（见附表）<span style="font-weight: bold; padding-left: 10px; cursor: pointer;">点击查看附件 <el-icon><QuestionFilled /></el-icon></span>，对项目成熟度进行自我评判（限500字以内）。</span>
            </el-tooltip>
            <el-dialog style="margin-top: 20px" v-model="annexEvaluationImage" width="50%">
              <img src="@/assets/prompt/evaluationImage.png" alt="国家工信部颁布的《技术成熟度等级划分及定义》" style="width: 100%;"/>
            </el-dialog>
            <div style="width:100%;height: calc(100vh - 160px);margin-top:10px;margin-bottom:10px;overflow: auto;">
              <el-form ref="ruleFormRef5" style="max-width: 100%;height: calc(100vh - 160px)" :model="ruleForm5" :rules="rules5" label-width="auto"  class="demo-ruleForm" :size="formSize" status-icon>
                <el-form-item  prop='projMaturityAssessment'>
                  <el-input class="inp_area" v-model="ruleForm5.projMaturityAssessment"  maxlength="500" style="width: 100%;font-size: 16px;" :placeholder="placeholderMsg4" show-word-limit type="textarea" />
                </el-form-item>
              </el-form>
            </div>
          </el-card>
          <el-card v-if="active===5" class="card_stl" shadow="always">
            <el-divider content-position="left"><span class="size20">*</span> <span class="size18">五、项目支持</span></el-divider>
            <span style="color:#F56C6C">注：简要介绍项目团队、资金等已具备的条件；项目所需支持的条件，包括资金、场地、产业资源、其他人员配备等（限1000字以内）。</span>
            <div style="width:100%;height: calc(100vh - 160px);margin-top:10px;margin-bottom:10px;overflow: auto;">
              <el-form ref="ruleFormRef6" style="max-width: 100%;" :model="ruleForm6" :rules="rules6" label-width="auto"  class="demo-ruleForm" :size="formSize" status-icon>
                <el-form-item  prop='projSupport'>
                  <el-input class="inp_area" v-model="ruleForm6.projSupport" :rows="20" maxlength="1000" style="width: 100%;font-size: 16px;" :placeholder="placeholderMsg5" show-word-limit type="textarea" />
                </el-form-item>
              </el-form>
            </div>
          </el-card>
          <el-card v-if="active===6" class="card_stl" shadow="always">
            <el-divider content-position="left"><span class="size20">*</span> <span class="size18">六、项目实施方案</span></el-divider>
            <span style="color:#F56C6C">注：主要阐述本项目需在哪些方面需进一步验证才能走向产业化，项目潜在风险点及其解决方案；本项目科技成果概念验证的具体内容、实施方案（限3000字以内）。</span>
            <div style="width:100%;height: calc(100vh - 300px);margin-top:10px;margin-bottom:10px;overflow: auto;">
              <el-form ref="ruleFormRef7" style="max-width: 100%;" :model="ruleForm7" :rules="rules7" label-width="auto"  class="demo-ruleForm" :size="formSize" status-icon>
                <div style="overflow:auto">
                  <el-form-item  prop='projImplPlan'>
                    <Editor v-model="ruleForm7.projImplPlan" ref="editorRef" style="width:100%;"  :defaultContent="defaultContent" :placeholder="placeholderMsg6" @change="change" />
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </el-card>
          <el-card v-if="active===7" class="card_stl" shadow="always">
            <el-divider content-position="left">
              <span class="size20">*</span>
              <span class="size18">七、项目目标及考核指标</span>
            </el-divider>
            <div class="tab_7">
              <table border="1" cellspacing="0" cellpadding="10" class="tableTD">
                <tr>
                  <th width="15%"><span class="cl_red">*</span>项目名称</th>
                  <td width="35%">{{ruleForm.pmName}}</td>
                  <th width="15%"><span class="cl_red">*</span>单位名称</th>
                  <td width="35%">{{ ruleForm.applyingOrgName }}</td>
                </tr>
              </table>
              <h4 class="h4_1"><div class="line-le"></div><div class="ml-3 mr-3" >项目资金（万元）</div><div class="line3"></div></h4>
              <el-form ref="ruleFormRefT8" style="max-width: 100%;" :model="ruleFormT8" :rules="rulesT8" label-width="auto"  class="demo-ruleForm" :size="formSize" status-icon>
                <table border="1" cellspacing="0" cellpadding="10" style="width:100%" class="tableTD">
                  <tr>
                    <th width="15%"><span class="cl_red">*</span>资金总额</th>
                    <td width="35%">{{ruleForm.totalBudgetWan}}（万元）
                    </td>
                    <th width="15%"><span class="cl_red">*</span>其中:申请专项资金</th>
                    <td width="35%">{{ruleForm.specialFundApplication}}（万元）
                    </td>
                  </tr>
                  <tr>
                    <th width="15%"><span class="cl_red">*</span>单位自筹资金</th>
                    <td width="35%">{{ruleForm.selfFunding}}（万元）
                    </td>
                    <th width="15%"><span class="cl_red">*</span>其他渠道获得资金</th>
                    <td width="35%">{{ruleForm.otherFunding}}（万元）
                    </td>
                  </tr>
                  <tr>
                    <th rowspan="2">绩效目标</th>
                    <td colspan="3">
                      <span class="size14"><b>根据项目特点撰写（建议：原理验证</b>重点目标是验证技术的可实现性；<b>产品与场景体系验证</b>重点验证产品实现的基础条件及应用场景体系实现过程中关键技术、加工测试工艺、系统集成验证等技术手段以及产业支持政策体系的可实现性；<b>原型制备与技术可行性验证验证</b>成果产业化的技术可行性及需要的基础条件，完成符合设计要求的小批量试制工艺技术体系验证，明确工程化过程中的技术风险；<b>商业前景验证</b>需形成推广应用的政策需求分析报告和市场竞争策略报告等；<b>其他类应</b>通过项目调研、创业咨询辅导、小批量试制、二次开发、中试熟化等产创融合链接服务实现投资转化）。</span>
                    </td>
                  </tr>
                  <tr>
                    <td colspan="3">
                      <el-form-item  prop='performanceTarget'>
                        <el-input  v-model="ruleFormT8.performanceTarget" :rows="5" maxlength="500" style="width: 100%;font-size: 16px;" placeholder="请填写绩效目标......" show-word-limit type="textarea" />
                      </el-form-item>
                    </td>
                  </tr>
                </table>
              </el-form>
              <el-form ref="ruleFormRef8" style="max-width: 100%;" :model="ruleForm8" :rules="rules8" label-width="auto"  class="demo-ruleForm" :size="formSize" status-icon>
                <!-- <h4 class='mb-3' class="h4_1"><div class="line-le"></div><span style="color:#F56C6C">*</span><div class="ml-3 mr-3" >项目实施方案</div><div class="line"></div></h4> -->
                <table border="0" cellspacing="0" cellpadding="10" style="width:100%;border-top:0px;" class="tableTD ">
                  <tr>
                    <th width="15%" style='border-top:0px;'>一级指标</th>
                    <th width="20%" style='border-top:0px;'>二级指标</th>
                    <th  width="30%" style='border-top:0px;'>
                      <el-tooltip placement="top"  content="点击查看三级指标填报示例">
                        <span  @click="showImage = true">三级指标<span style="color: #6b6b6c;font-size: 12px; padding-left: 10px; cursor: pointer;">点击查看参考 <el-icon><QuestionFilled /></el-icon></span></span>
                      </el-tooltip>
                      <el-dialog v-model="showImage" width="50%">
                        <img src="@/assets/prompt/indicator-tips.png" alt="三级指标填报示例" style="width: 100%;"/>
                      </el-dialog>
                    </th>
                    <th style='border-top:0px;'>考核方式方法</th>
                    <th style='border-top:0px;'>指标分值</th>
                  </tr>
                  <tr v-for='(itemOne,index) in treeData' :key='index+"o"'>
                    <th>{{ itemOne.label }}</th>
                    <td colspan="4" valign="top" style='padding:0px;'>
                      <table border cellspacing="0" cellpadding="0" style="width:100%;" class="tableTD2 ">
                        <tr  v-for='(itemTwo,index) in itemOne.children' :key="index+'w'">
                          <th width='23.5%' >
                            <span>{{itemTwo.label }}</span>
                            <el-button type="text" @click="get11DataAdd(itemTwo,'1')" ><el-icon :size="19" class='ml-2' ><CirclePlus /></el-icon></el-button>
                            <el-button type="text" @click="get11DataAdd(itemTwo,'2')" ><el-icon :size="19"><Remove /></el-icon></el-button>
                          </th>
                          <td>
                            <table border cellspacing="0" cellpadding="0" style="width:100%;" class="tableTD2 ">
                              <tr v-for='(arritem, index) in arrChildren' :key="index+'ic'" v-show="(arritem.parentId==itemTwo.value)">
                                <td width='46.2%' style="padding:5px;border-left:0;" align="center">
                                  <el-form-item prop="indicatorName" >
                                    <el-input class='centered-input' v-model="arritem.indicatorName"  placeholder="请填写三级指标" />
                                  </el-form-item>
                                </td>
                                <td width='30.8%' style="padding:5px;border-left:0;">
                                  <el-form-item prop="assessmentMethod">
                                    <el-select class='centered-input' v-model="arritem.assessmentMethod" placeholder="请选择考核方式">
                                      <el-option v-for="item_mentod in pmAssessmentMethod" :key="item_mentod.value" :label="item_mentod.label" :value="item_mentod.value.toString()" />
                                    </el-select>
                                  </el-form-item>
                                </td>
                                <td style="padding:5px;border-left:0;">
                                  <el-form-item prop="indicatorScore">
                                    <el-input class='centered-input' v-model="arritem.indicatorScore" placeholder="请填写指标分值" />
                                  </el-form-item>
                                </td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </el-form>
            </div>
          </el-card>
          <el-card v-if="active===8" class="card_stl" shadow="always">
            <el-divider content-position="left"><span class="size20">*</span> <span class="size18">八、项目团队基本情况表</span></el-divider>
            <div class="div_8_1">
              <el-button type="primary" @click="newUserBtn" class='mb-3' :icon="Plus">新增</el-button>
              <el-table :data="tableData"  class="custom-table-header tab_8" :header-cell-style="{'background':'#FAFAFA','text-align':'center','padding':'15px 2px'}" :cell-style="{'text-align':'center'}" border >
                <el-table-column prop="date" label="序号" width='60' fixed>
                  <template #default='{ $index }'>{{ $index+1 }}</template>
                </el-table-column>
                <el-table-column prop="memberName" label="姓名" width='110' fixed />
                <el-table-column prop="gender" label="性别"   width='60'>
                  <template  #default='scope'><span>{{ scope.row.gender==='1'?'男':'女' }}</span></template>
                </el-table-column>
                <el-table-column prop="birthdate" label="出生年月"  width="130" />
                <el-table-column prop="idCardNumber" label="身份证号码" width="200"  />
                <el-table-column prop="technicalTitle" label="专业技术职称" width="230">
                  <template #default='scope'>
                    <span>
                      {{
                        pmTitle
                          .find(item => item.value.toString() === scope.row.technicalTitle)
                          ?.label
                        ?? '未选择'
                      }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="position" label="职务" width="180"  />
                <el-table-column prop="highestDegree" label="最高学位" width="180">
                  <template #default='scope'>
                         <span>{{ scope.row.highestDegree ? pmHighestDegree.find(item => item.value.toString() === scope.row.highestDegree)?.label : '未选择' }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="major" label="专业" width="180"  />
                <el-table-column prop="fullTimeHoursPerMonth" label="投入本项目的全时工作时间" width='230' />
                <el-table-column prop="workUnit" label="工作单位" width="180"  />
                <el-table-column prop="mainResponsibilities" label="承担的主要工作" width="180"  />
                <el-table-column label="操作" width='160' fixed="right">
                  <template #default='scope'>
                    <el-button type="primary" @click="editFun(scope.row)"  plain>修改</el-button>
                    <el-button type="danger" :disable="scope.$index==0" @click="deleteFun(scope.row.id)"  plain>删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <Pagination :total="total7" v-model:page="queryParamsUserLIst.pageNo" v-model:limit="queryParamsUserLIst.pageSize" @pagination="getUserLIst" />
              <!-- </el-form> -->
            </div>
            <el-dialog v-model="dialogFormVisible" :title="titleMsg"  width="43%">
              <el-form  ref="ruleFormRef9" class="demo-ruleForm ml-3 mr-3" :model="ruleForm9" :rules="rules9" :size="formSize" status-icon>
                <div class="mb-3 ml-2 mt-1 pl-2 linet">基本信息</div>
                <el-card shadow="never">
                <el-row justify="center" class="flex_row">
                  <el-col :span="10" >
                    <el-form-item label="姓名:" prop="memberName"  class='mt-1'>
                      <el-input v-model="ruleForm9.memberName" placeholder="请填写姓名"  />
                    </el-form-item>
                  </el-col>
                  <el-col :span="10" class="ml-7">
                    <el-form-item label="性别:"  prop="gender" class='mt-1' >
                      <el-radio-group v-model="ruleForm9.gender">
                        <el-radio value="1" size="large">男</el-radio>
                        <el-radio value="2" size="large">女</el-radio>
                      </el-radio-group>
                </el-form-item>
                  </el-col>
                </el-row>
                <el-row justify="center" class="flex_row">
                  <el-col :span="10">
                    <el-form-item label="身份证号码:" prop="idCardNumber" class='mt-4' >
                      <el-input v-model="ruleForm9.idCardNumber" placeholder="请填写身份证号码"  @change="getBirthdate(ruleForm9.idCardNumber,'birthdate')" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="10" class="ml-7">
                    <el-form-item label="出生年月:"  prop="birthdate" class='mt-4' >
                      <el-date-picker v-model="ruleForm9.birthdate" type="date" value-format="YYYY-MM-DD" placeholder="请选择" />
                    </el-form-item>
                  </el-col>
                </el-row >
                </el-card>
                <div class="mb-3 mt-4 ml-2 pl-2 linet">其他</div>
                <el-card shadow="never" >
                <el-row justify="center" class="flex_row">
                  <el-col :span="10">
                    <el-form-item label="专业技术职称:" prop="technicalTitle" >
                  <!-- <el-input v-model="ruleForm9.technicalTitle" placeholder="请填写专业技术职称"  /> -->
                    <el-select v-model="ruleForm9.technicalTitle" >
                      <el-option v-for="itemhd in pmTitle" :key='itemhd.value' :label="itemhd.label" :value="itemhd.value.toString()" placeholder="请选择" />
                    </el-select>
                </el-form-item>
                  </el-col>
                  <el-col :span="10" class="ml-7">
                    <el-form-item label="职务:" prop="position" >
                      <el-input v-model="ruleForm9.position"   placeholder="请填写职务" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row justify="center" class="flex_row">
                  <el-col :span="10">
                    <el-form-item label="最高学位:" prop="highestDegree" class='mt-4' >
                      <el-select v-model="ruleForm9.highestDegree" >
                        <el-option v-for="itemhd in pmHighestDegree" :key='itemhd.value' :label="itemhd.label" :value="itemhd.value.toString()" placeholder="请选择" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="10" class="ml-7">
                    <el-form-item label="专业:" prop="major" class='mt-4' >
                      <el-input v-model="ruleForm9.major" placeholder="专业"  />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row justify="center" class="flex_row">
                  <el-col :span="10">
                    <el-form-item label="投入本项目的全时工作时间:" prop="fullTimeHoursPerMonth"  class='mt-4 mb-4'>
                      <el-input v-model="ruleForm9.fullTimeHoursPerMonth" placeholder="如：xx（月）"  />
                    </el-form-item>
                  </el-col>
                  <el-col :span="10" class="ml-7">
                    <el-form-item label="工作单位:" prop="workUnit"  class='mt-4' >
                      <el-input v-model="ruleForm9.workUnit" placeholder="请填写工作单位"  />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row justify="center" class="flex_row">
                  <el-col :span="21">
                    <el-form-item label="承担的主要工作:" prop="mainResponsibilities"  class='mt-4'>
                      <el-input v-model="ruleForm9.mainResponsibilities" placeholder="请填写承担的主要工作" style="width:100%;"  />
                    </el-form-item>
                  </el-col>
                </el-row>
                </el-card>
              </el-form>
              <template #footer>
                <div class="dialog-footer">
                  <el-button @click="rttRlue()">取 消</el-button>
                  <el-button type="primary" @click="addUserFun(ruleFormRef9)">确 定</el-button>
                </div>
              </template>
            </el-dialog>
          </el-card>
          <el-card v-if="active===9" class="card_stl" shadow="always">
            <el-divider content-position="left"><span class="size20">*</span> <span class="size18">九、项目经费支出</span></el-divider>
            <div class="tab_9">
              <div class="mb-3">
                <h4>项目支出范围（参考）</h4>
                      <p class="size14">
                        （一）直接费用包括：<br />
                        设备费：在项目实施过程中对现有仪器设备进行升级改造，设备租赁等发生的费用。专项资金原则上不支持购置设备，鼓励使用公共服务平台开展项目验证。<br />
                        业务费：在项目实施过程中消耗的各种材料、辅助材料等低值易耗品的采购、运输、装卸、整理等费用，发生的测试化验加工、燃料动力、出版/文献/信息传播/知识产权事务、会议/差旅/国际合作交流等费用，以及其他相关支出。<br />
                        劳务费：在项目实施过程中支付给参与项目的研究生、博士后、访问学者和项目聘用的研究人员、科研辅助人员等的劳务性费用，以及支付给临时聘请的咨询专家的费用等。<br />
                      </p>
                      <p class="size14">
                        （二）间接费用是指承担单位在组织实施项目过程中发生的无法在直接费用中列支的相关费用。主要包括：承担单位为项目研究提供的房屋占用，日常水、电、气、暖等消耗，有关管理费用的补助支出等。
                      </p>
              </div>
              <el-form ref="ruleFormRef10" :model="ruleForm10" :rules="rules10" label-width="auto"  class="demo-ruleForm"  style="max-width: 100%;" :size="formSize" status-icon>
                <table border="1" cellspacing="0" cellpadding="10" class="tableTD">
                  <tr>
                    <th width="5%">序号</th>
                    <th width="15%">支出项目</th>
                    <th colspan="4" width="50%">支出金额（单位：万元）</th>
                    <th rowspan="2">测算依据及说明</th>
                  </tr>
                  <tr>
                    <th>（一）</th>
                    <th>直接费用</th>
                    <th>申请专项资金</th>
                    <th>单位自筹资金</th>
                    <th>其他渠道获得资金</th>
                    <th>合计</th>
                  </tr>
                  <tr>
                    <td align="center">1</td>
                    <td>设备费</td>
                    <td>
                      <el-form-item  prop="equipmentSpecial">
                        <el-input v-model="ruleForm10.equipmentSpecial"  @input="calculateResult(ruleForm10.equipmentSpecial,ruleForm10.equipmentSelf,ruleForm10.equipmentOther,'equipmentTotal'),calculateResultLie(ruleForm10.equipmentSpecial,ruleForm10.businessSpecial,ruleForm10.laborSpecial,ruleForm10.utilitiesSpecial,ruleForm10.testingSpecial,ruleForm10.performanceSpecial,ruleForm10.indirectOtherSpecial,'specialTotal')"
                           />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="equipmentSelf">
                        <el-input v-model="ruleForm10.equipmentSelf" @input="calculateResult(ruleForm10.equipmentSpecial,ruleForm10.equipmentSelf,ruleForm10.equipmentOther,'equipmentTotal'),calculateResultLie(ruleForm10.equipmentSelf,ruleForm10.businessSelf,ruleForm10.laborSelf,ruleForm10.utilitiesSelf,ruleForm10.testingSelf,ruleForm10.performanceSelf,ruleForm10.indirectOtherSelf,'specialSelf')"
                           />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="equipmentOther">
                        <el-input v-model="ruleForm10.equipmentOther" @input="calculateResult(ruleForm10.equipmentSpecial,ruleForm10.equipmentSelf,ruleForm10.equipmentOther,'equipmentTotal'),calculateResultLie(ruleForm10.equipmentOther,ruleForm10.businessOther,ruleForm10.laborOther,ruleForm10.utilitiesOther,ruleForm10.testingOther,ruleForm10.performanceOther,ruleForm10.indirectOther,'specialOther')"
                           />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="equipmentTotal">
                        <el-input
                          class="inp_bold"
                          readonly
                          v-model="ruleForm10.equipmentTotal"
                          v-if="ruleForm10.equipmentTotal != null
                            ? calculateResultLie(
                                ruleForm10.equipmentTotal,
                                ruleForm10.businessTotal,
                                ruleForm10.laborTotal,
                                ruleForm10.utilitiesTotal,
                                ruleForm10.testingTotal,
                                ruleForm10.performanceTotal,
                                ruleForm10.indirectTotal,   // ← 新增这一项
                                'totalFunding'
                              )
                            : ''"
                        />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item prop="equipmentBasisNotes">
                        <el-input v-model="ruleForm10.equipmentBasisNotes" type="textarea" />
                      </el-form-item>
                    </td>
                  </tr>
                  <tr>
                    <td align="center">2</td>
                    <td>业务费</td>
                    <td>
                      <el-form-item  prop="businessSpecial">
                        <el-input v-model="ruleForm10.businessSpecial" @input="calculateResult(ruleForm10.businessSpecial,ruleForm10.businessSelf,ruleForm10.businessOther,'businessTotal'),calculateResultLie(ruleForm10.equipmentSpecial,ruleForm10.businessSpecial,ruleForm10.laborSpecial,ruleForm10.utilitiesSpecial,ruleForm10.testingSpecial,ruleForm10.performanceSpecial,ruleForm10.indirectOtherSpecial,'specialTotal')"
                            />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="businessSelf">
                        <el-input v-model="ruleForm10.businessSelf" @input="calculateResult(ruleForm10.businessSpecial,ruleForm10.businessSelf,ruleForm10.businessOther,'businessTotal'),calculateResultLie(ruleForm10.equipmentSelf,ruleForm10.businessSelf,ruleForm10.laborSelf,ruleForm10.utilitiesSelf,ruleForm10.testingSelf,ruleForm10.performanceSelf,ruleForm10.indirectOtherSelf,'specialSelf')"
                          />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="businessOther">
                        <el-input v-model="ruleForm10.businessOther" @input="calculateResult(ruleForm10.businessSpecial,ruleForm10.businessSelf,ruleForm10.businessOther,'businessTotal'),calculateResultLie(ruleForm10.equipmentOther,ruleForm10.businessOther,ruleForm10.laborOther,ruleForm10.utilitiesOther,ruleForm10.testingOther,ruleForm10.performanceOther,ruleForm10.indirectOther,'specialOther')"
                          />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="businessTotal">
                        <el-input class="inp_bold" readonly v-model="ruleForm10.businessTotal" v-if="ruleForm10.businessTotal!=null?calculateResultLie(ruleForm10.equipmentTotal,ruleForm10.businessTotal,ruleForm10.laborTotal,ruleForm10.utilitiesTotal,ruleForm10.testingTotal,ruleForm10.performanceTotal,ruleForm10.indirectTotal,'totalFunding'):''" />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item prop="businessBasisNotes">
                        <el-input v-model="ruleForm10.businessBasisNotes" type="textarea" />
                      </el-form-item>
                    </td>
                  </tr>
                  <tr>
                    <td align="center">3</td>
                    <td>劳务费</td>
                    <td>
                      <el-form-item  prop="laborSpecial">
                        <el-input v-model="ruleForm10.laborSpecial" @input="calculateResult(ruleForm10.laborSpecial,ruleForm10.laborSelf,ruleForm10.laborOther,'laborTotal'),calculateResultLie(ruleForm10.equipmentSpecial,ruleForm10.businessSpecial,ruleForm10.laborSpecial,ruleForm10.utilitiesSpecial,ruleForm10.testingSpecial,ruleForm10.performanceSpecial,ruleForm10.indirectOtherSpecial,'specialTotal')"
                            />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="laborSelf">
                        <el-input v-model="ruleForm10.laborSelf" @input="calculateResult(ruleForm10.laborSpecial,ruleForm10.laborSelf,ruleForm10.laborOther,'laborTotal'),calculateResultLie(ruleForm10.equipmentSelf,ruleForm10.businessSelf,ruleForm10.laborSelf,ruleForm10.utilitiesSelf,ruleForm10.testingSelf,ruleForm10.performanceSelf,ruleForm10.indirectOtherSelf,'specialSelf')"
                           />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="laborOther">
                        <el-input v-model="ruleForm10.laborOther" @input="calculateResult(ruleForm10.laborSpecial,ruleForm10.laborSelf,ruleForm10.laborOther,'laborTotal'),calculateResultLie(ruleForm10.equipmentOther,ruleForm10.businessOther,ruleForm10.laborOther,ruleForm10.utilitiesOther,ruleForm10.testingOther,ruleForm10.performanceOther,ruleForm10.indirectOther,'specialOther')"
                           />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="laborTotal">
                        <el-input class="inp_bold" readonly v-model="ruleForm10.laborTotal" v-if="ruleForm10.laborTotal!=null?calculateResultLie(ruleForm10.equipmentTotal,ruleForm10.businessTotal,ruleForm10.laborTotal,ruleForm10.utilitiesTotal,ruleForm10.testingTotal,ruleForm10.performanceTotal,ruleForm10.indirectTotal,'totalFunding'):''"  />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item prop="laborBasisNotes">
                        <el-input v-model="ruleForm10.laborBasisNotes" type="textarea" />
                      </el-form-item>
                    </td>
                  </tr>
                  <tr>
                    <th>（二）</th>
                    <th>间接费用</th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                  </tr>
                  <tr>
                    <td align="center">1</td>
                    <td>间接费用</td>
                    <td>
                      <el-form-item prop="indirectSpecial">
                        <el-input v-model="ruleForm10.indirectSpecial" @input="calculateResult(ruleForm10.indirectSpecial,ruleForm10.indirectSelf,ruleForm10.indirectOther,'indirectTotal'),calculateResultLie(ruleForm10.equipmentSpecial,ruleForm10.businessSpecial,ruleForm10.laborSpecial,ruleForm10.indirectSpecial,'specialTotal')" />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item prop="indirectSelf">
                        <el-input v-model="ruleForm10.indirectSelf" @input="calculateResult(ruleForm10.indirectSpecial,ruleForm10.indirectSelf,ruleForm10.indirectOther,'indirectTotal'),calculateResultLie(ruleForm10.equipmentSelf,ruleForm10.businessSelf,ruleForm10.laborSelf,ruleForm10.indirectSelf,'specialSelf')" />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item prop="indirectOther">
                        <el-input v-model="ruleForm10.indirectOther" @input="calculateResult(ruleForm10.indirectSpecial,ruleForm10.indirectSelf,ruleForm10.indirectOther,'indirectTotal'),calculateResultLie(ruleForm10.equipmentOther,ruleForm10.businessOther,ruleForm10.laborOther,ruleForm10.indirectOther,'specialOther')" />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item prop="indirectTotal">
                        <el-input class="inp_bold" readonly v-model="ruleForm10.indirectTotal" v-if="ruleForm10.indirectTotal!=null?calculateResultLie(ruleForm10.equipmentTotal,ruleForm10.businessTotal,ruleForm10.laborTotal,ruleForm10.indirectTotal,'totalFunding'):''" />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item prop="indirectBasisNotes">
                        <el-input v-model="ruleForm10.indirectBasisNotes" type="textarea" />
                      </el-form-item>
                    </td>
                  </tr>
                  <tr>
                    <th colspan="2">合计</th>
                    <td>
                      <el-form-item  prop="specialTotal">
                        <el-input class="inp_bold" readonly v-model="ruleForm10.specialTotal" />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="specialSelf">
                        <el-input class="inp_bold" readonly v-model="ruleForm10.specialSelf"  />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="specialOther">
                        <el-input class="inp_bold" readonly v-model="ruleForm10.specialOther"  />
                      </el-form-item>
                    </td>
                    <td>
                      <el-form-item  prop="totalFunding">
                        <el-input class="inp_bold" readonly v-model="ruleForm10.totalFunding"  />
                      </el-form-item>
                    </td>
                  </tr>
                </table>
              </el-form>
            </div>
          </el-card>
          <!-- 第十一步：项目附件上传 -->
          <el-card v-if="active===10" class="card_stl" shadow="always">
            <ProjAttachmentUpload ref="attachmentUploadRef" />
          </el-card>
        </div>
        <!-- <el-button type="primary" @click="submitForm(ruleFormRef)">Create</el-button>
<el-button @click="resetForm(ruleFormRef)">Reset</el-button> -->
        <div style="width:100%;text-align: center;">
          <el-button type="info" plain style="margin-top: 12px" @click="back()">返 回</el-button>
          <el-button v-if="active!=0" class="bt_next" @click="last">上一步</el-button>
          <el-button v-else class="bt_next" disabled style="visibility: hidden;">上一步</el-button>
          <el-button v-show="active!=10" class="bt_next" :loading="isButtonDisabled||active===10" @click="submitForm(1)">下一步</el-button>
          <el-button type='primary'  @click="submitForm(2)" style="margin-top: 12px">保 存</el-button>
          <el-button  v-show="active===10" class="mt_12" type='primary'  @click="submitForm(3)">提 交</el-button>
          <!-- <el-button type="primary" style="margin-top: 12px"  @click="baoCun">保 存</el-button> -->
        </div>
      </el-col>
    </el-row>


  </div>
  <!-- </div> -->
</template>
<script setup lang="ts">
// @ts-nocheck
// 1 引入useRouter路由信息方法
import { useRoute,useRouter } from 'vue-router'
// 2 获取实例
const route = useRoute();
const { id } = route.query;
import { Editor } from '@/components/Editor'
import { onMounted, reactive, ref,toRaw, watch, nextTick } from 'vue'
import { ProjBasicApi, ProjBasicVO } from '@/api/pm/projbasic'
import { getUserProfile } from '@/api/system/user/profile'
import { SubjectInfoApi, SubjectInfoVO } from '@/api/pm/subjectinfo'
import {ComponentSize, ElMessageBox, FormInstance, FormRules} from 'element-plus'
import {QuestionFilled, Plus} from '@element-plus/icons-vue'
import {getStrDictOptions} from '@/utils/dict'//获取字典
import { ElMessage } from 'element-plus';
import {validateIDCard,validatePhone,validateTelephone,checkInputPositiveRealNum,decimalNumber,decimalNumber2} from '@/utils/validation';
// 引入组件
import ProjBasicInfoForm from './components/ProjBasicInfoForm.vue'
import ProjAttachmentUpload from './components/ProjAttachmentUpload.vue'

/** 成果概念验证项目基本信息 表单 */
defineOptions({ name: 'ProjBasicForm' })
const loading = ref(false);
const { t } = useI18n() // 国际化
const width=ref('80%')
const row1 = ref('2');
const row11 = ref('1');
const row11Data = ref([]);
const row12 = ref('1');
const row12Data = ref([]);
const newMsgArr = ref<IndicatorItem[]>([]);
// 定义三级指标项的类型
interface IndicatorItem {
  parentId: string
  indicatorName: string
  assessmentMethod: string
  indicatorScore: string
  id?: string
  value?: string
  sort?: any
  indicatorLevel?: string
  projectId?: string
}

// 将 arrChildren 从 ref([]) 改成带类型的 ref
const arrChildren = ref<IndicatorItem[]>([])
const  isButtonDisabled=ref(false);//按钮防抖
// 定义树节点类型
interface TreeItem {
  label: string
  value: string
  id?: string
  sort?: any
  indicatorScore?: string
  assessmentMethod?: string
  indicatorLevel?: string
  parentId?: any
  projectId?: string
  children?: TreeItem[]
}
// 将 treeData 从 ref([]) 改成 ref<TreeItem[]>([])
const treeData = ref<TreeItem[]>([])
const dialogFormVisible = ref(false);//步骤9弹窗
const tableData = ref([])
const timeMsg = ref();
const showImage = ref(false);
const annexEvaluationImage = ref(false);
 const chanFun=()=>{
   ruleForm.projectStartDate = toRaw(ruleForm.timeMsg)[0];
   ruleForm.projectEndDate = toRaw(ruleForm.timeMsg)[1];
}
const get11DataAdd = (arr, e) => {
  if (e === '1') {
    arrChildren.value.push({ parentId: arr.value, id:'', indicatorLevel: '3', indicatorName: '', indicatorScore: "", assessmentMethod: "", sort: arr.sort });
  }
  if (e == '2') {
    newMsgArr.value = [];
    for (let key in arr) {
      arrChildren.value.forEach((item, index) => {
        if (arr.value === item.parentId) {
          newMsgArr.value.push(item);
        }
      });
    }
    const msg = newMsgArr.value.pop();
    if (msg) { // 添加 msg 是否存在的检查
      const index = arrChildren.value.findIndex(obj => JSON.stringify(obj) === JSON.stringify(msg));
      ElMessageBox.confirm(
        '是否确定删除此数据?',
        '提示:',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(() => {
          if (msg.id && msg.id !== "") {
            ProjBasicApi.assessmentDelete(Number(msg.id))
          }
           if (index !== -1) {
            arrChildren.value.splice(index, 1);
          }
          ElMessage({ type: 'success', message: '已删除...', });

        })
        .catch(() => {
          ElMessage({ type: 'info', message: '已取消' });
        })
    }
  }
}

//其他参与人员信息
const userAddFun=()=>{
  otherUserArr.value.push({projectId:'', orgName:'', orgQuality:'', id: ''})
}
const userDelFun = (index,id) => {
   ElMessageBox.confirm(
    '是否确定删除此数据？',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
     .then(async () => {
      if(!id || id === '' || id === undefined){
         otherUserArr.value.splice(index, 1);
         ElMessage({ type: 'success', message: '删除成功', });
      } else {
        try {
          await ProjBasicApi.otherOrgDelete(Number(id));
          ElMessage({ type: 'success', message: '删除成功', });
          // 删除成功后刷新数据
          getEidtData2(toRaw(projectId.value), ProjBasicApi.getOtherOrg, otherUserArr,0);
        } catch (error) {
          console.error('删除失败:', error);
          ElMessage({ type: 'error', message: '其他参与机构不存在', });
        }
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '取消删除',
      })
    })
}




const otherUserArr = ref<OrgItem[]>([{ projectId: '', orgName: '', orgQuality: '', id: '' }])




const newMsg = ref(0);//9新增/编辑
const rowId = ref();//编辑id
const titleMsg = ref('');//弹窗表头
const newUserBtn = () => {//新增btn
  for (let msg in toRaw(resetForm9)) {
    toRaw(resetForm9)[msg]=""
  }
  dialogFormVisible.value = true;
  titleMsg.value = '新增人员信息：';
  newMsg.value = 0;
}



const addUserFun = async (formEl: FormInstance | undefined) => {//提交
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (newMsg.value === 0) {
        ruleForm9.projectId = projectId.value;
        ProjBasicApi.createTeamInfoBasic(ruleForm9).then((res) => {
          rttRlue();
          ElMessage({ message: '新增成功！', type: 'success' });
          getUserLIst();
        })
      } else {
        ruleForm9.id = rowId.value;
        ProjBasicApi.teamInfoUpdate(ruleForm9).then((res) => {
          rttRlue();
          ElMessage({ message: '修改成功！', type: 'success' });
          getUserLIst();
        })
      }
    } else {
    }
  })

}
//获取人员列表名单
const total7 = ref(0) // 列表的总页数
const queryParamsUserLIst = reactive({
  pageNo:1,
  pageSize: 10,
  projectId:'',
})
const getUserLIst = () => {//获取列表信息
  queryParamsUserLIst.projectId = projectId.value;
  ProjBasicApi.teamInfopage(queryParamsUserLIst).then((res) => {
    tableData.value = res.list;
    total7.value = res.total;
  })
}
const editFun = (row) => {// 编辑
  titleMsg.value='修改人员信息：'
  newMsg.value = 1;
  rowId.value = row.id;
   //编辑按钮事件 要放在nextTick里面 赋值的时候
   nextTick(() =>{
     // Object.assign(updateList,row)
      for(let item in row){
    for (let key in ruleForm9) {
      if (item === key) {
        ruleForm9[key] = row[key];
      }
    }
  }
   })
  dialogFormVisible.value = true;

}
const deleteFun = async(id:number) => {//删除
  try {
    await ProjBasicApi.teamInfoDelete(id);
    ElMessage({ message: '删除成功！', type: 'success' });
    await getUserLIst();//刷新
  } catch { }
}




const props1 = {children: 'children',  label: 'name',value: 'id' };
const pmValidationCategory = ref(getStrDictOptions(DICT_TYPE.PM_VALIDATION_CATEGORY));//验证类别
const pmType = ref(getStrDictOptions(DICT_TYPE.PM_TYPE));//验证类别
const pmConversionRegion = ref(getStrDictOptions(DICT_TYPE.PM_CONVERSION_REGION));//拟转化地域
const pmIdType = ref(getStrDictOptions(DICT_TYPE.PM_ID_TYPE));//证件类型
const pmHighestDegree = ref(getStrDictOptions(DICT_TYPE.PM_HIGHEST_DEGREE));//最高学位
const pmTitle = ref(getStrDictOptions(DICT_TYPE.PM_TITLE));//职称
const pmAssessmentMethod = ref(getStrDictOptions(DICT_TYPE.PM_ASSESSMENT_METHOD));//职称
// 学科数据
const ttttvData=ref()
SubjectInfoApi.getTreeSubjectInfo().then((res) => {
  ttttvData.value = res;
})
//机构树
const options = ref([]);
ProjBasicApi.getAreaTree().then((res) => {
  options.value=res;
})
interface InputItem {
  xh: string;
  dwmc: string;
  dwxz: string;
}

const back = () => {
  ElMessageBox.confirm(
    '返回列表前请确保数据是否保存?',
    {
      confirmButtonText: '是',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      ElMessage({ type: 'success', message: '操作成功' });
      router.push('/projbasic');
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消',
      })
    })

}


const nextMsg = ref(false);
//步骤条
const active = ref(0);

// 步骤列表
const stepList = ref([
  { title: '项目基本信息表' },
  { title: '一、项目简介' },
  { title: '二、项目基本情况介绍' },
  { title: '三、项目预期目标与产业化规划' },
  { title: '四、项目成熟度自我评判' },
  { title: '五、项目支持' },
  { title: '六、项目实施方案' },
  { title: '七、项目目标及考核指标' },
  { title: '八、项目团队基本情况表' },
  { title: '九、项目经费支出' },
  { title: '项目附件上传' }
])

// 步骤点击
const stepClick = (stepIndex: number) => {
  if (stepIndex <= active.value) {
    active.value = stepIndex;
    getlastFun();
  }
}
const next = () => {
  if (active.value++ > 10) active.value = 0

  // 在步骤切换后调用getlastFun加载数据
  console.log('步骤切换到:', active.value)
  getlastFun()
}

const last = () => {
  ElMessageBox.confirm(
    '以防数据丢失，请确保数据保存后再进行"上一步"或"返回"操作！',
    {
      confirmButtonText: '已保存',
      cancelButtonText: '去保存',
      type: 'warning',
    }
  ).then(() => {
    ElMessage({ type: 'success', message: '操作成功' });
    if (active.value-- == 0) {active.value = 0;}
      getlastFun();//获取上一步内容
  })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消',
      })
    })

}

//自动获取生日日期ruleForm9.
const getBirthdate = (val, birthdate) => {
  if (val.length === 18) {
    const year = val.substring(6, 10);
    const month = val.substring(10, 12);
    const day = val.substring(12, 14);
    ruleForm9.birthdate= `${year}-${month}-${day}`;
    }

}

const getlastFun = () => {
    if (active.value==0) {
      // 获取第一步基本信息数据
      ProjBasicApi.getProjBasic(projectId.value).then(basicData => {
        if (basicData) {
          // 更新父组件的表单数据
          Object.assign(ruleForm, basicData);
          // 更新子组件的表单数据
          if (projBasicInfoFormRef.value) {
            projBasicInfoFormRef.value.setFormData(basicData);
          }
        }
      });
      
      // 查询其他参与机构数据并更新到子组件
      ProjBasicApi.getOtherOrg({ projectId: projectId.value, pageNo: 1, pageSize: 10 }).then(res => {
          if (res && res.list && res.list.length > 0) {
              otherUserArr.value = res.list;
              // 更新子组件的其他参与机构数据
              if (projBasicInfoFormRef.value) {
                  projBasicInfoFormRef.value.setOtherOrgData(res.list);
              }
          } else {
              otherUserArr.value = [{ projectId: '', orgName: '', orgQuality: '' }];
              if (projBasicInfoFormRef.value) {
                  projBasicInfoFormRef.value.setOtherOrgData(otherUserArr.value);
              }
          }
      });
    }
    if (active.value == 1) {
            getEidtData(toRaw(projectId.value),ProjBasicApi.getOverview,ruleForm2)
      }
    if (active.value == 2) {
        getEidtData(toRaw(projectId.value), ProjBasicApi.getIntroductions, ruleForm3)//编辑时获取数据
        }
    if (active.value === 3) {
        getEidtData(toRaw(projectId.value), ProjBasicApi.getGoalsPlan, ruleForm4)
        }
    if (active.value == 4) {
        getEidtData(toRaw(projectId.value), ProjBasicApi.getMaturityAssessment, ruleForm5)
        }
    if (active.value == 5) {
      getEidtData(toRaw(projectId.value), ProjBasicApi.getSupport, ruleForm6)
        }
  if (active.value === 6) {
      getEidtData(toRaw(projectId.value), ProjBasicApi.getImplPlan, ruleForm7)
    }
    if (active.value == 7) {
      getEidtData(toRaw(projectId.value),ProjBasicApi.getAssessmentCriteria,ruleForm8),getEidtData(toRaw(projectId.value), ProjBasicApi.getGoalsPage, ruleFormT8)
        }
    // if (active.value == 8) {
    //   }
    if (active.value == 9) {
      getEidtData(toRaw(projectId.value), ProjBasicApi.getBudgetExpenses, ruleForm10);
    }
    if (active.value == 10) {
      nextTick(() => {
        if (attachmentUploadRef.value && projectId.value) {
          console.log('正在加载第11步（项目附件）数据，项目ID:', projectId.value);
          // 先禁用自动加载，避免触发watch
          attachmentUploadRef.value.disableAutoLoad && attachmentUploadRef.value.disableAutoLoad();

          // 先设置项目ID
          attachmentUploadRef.value.setProjectId(projectId.value);

          // 清除现有数据，防止累积
          attachmentUploadRef.value.clearAttachments && attachmentUploadRef.value.clearAttachments();

          // 只使用一种方式加载数据
          console.log('开始调用API获取项目材料');
          ProjBasicApi.getMaterials(projectId.value).then(materials => {
            console.log('获取到的项目材料原始数据:', materials);

            // 检查返回的数据是否有效
            if (!materials) {
              console.warn('API返回的数据为空');
              return;
            }

            // 检查是否有list字段
            if (materials.list && Array.isArray(materials.list)) {
              console.log('API返回的数据包含list字段，长度:', materials.list.length);
              console.log('list数据:', JSON.stringify(materials.list));
            }

            if (attachmentUploadRef.value) {
              // 直接使用 setAttachments 方法设置数据，它会处理各种数据格式
              console.log('调用setAttachments设置附件数据');
              attachmentUploadRef.value.setAttachments(materials);

              // 确保视图更新
              nextTick(() => {
                console.log('视图更新后DOM中的文件列表元素数量:',
                  document.querySelectorAll('.custom-upload-file').length);
              });
            }
          }).catch(error => {
            console.error('获取项目材料失败:', error);
            ElMessage.error('加载项目附件数据失败，请刷新页面重试');
          });
        } else {
          console.warn('附件组件或项目ID不存在:',
                       { attachmentUploadRef: !!attachmentUploadRef.value, projectId: projectId.value });
        }
      });
    }
}

//验证1
interface RuleForm {
  province:string
  city:string
  county:string
  pmName: string
  academicFieldOne: string
  academicFieldTwo: string
  academicFieldThree: string
  allOrgAddress:string[]
  discipline:string[]
  validationCategory: string
  pmType: string
  conversionRegion: string
  totalBudgetWan: string
  specialFundApplication: string
  selfFunding: string
  otherFunding: string
  timeMsg:string[]
  projectStartDate:string
  projectEndDate:string
  dwmc: string
  applyingOrgAddress: string
  applyingOrgId:string
  applyingOrgName:string
  managerName: string
  applyingDate:string
  managerGender: string
  managerBirthDate: string
  managerIdType: string
  managerIdNumber: string
  managerHighestDegree: string
  managerTitle:string
  managerPosition: string
  managerEmail: string
  managerMobile: string
  contactName: string
  contactEmail: string
  contactPhone: string
  contactMobile: string
  contactIdType: string
  contactIdNumber: string
  totalParticipants: string
  seniorTitleCount: string
  mediumTitleCount: string
  juniorTitleCount: string
  otherTitleCount: string
  doctorDegreeCount: string
  masterDegreeCount: string
  bachelorDegreeCount: string
  otherDegreeCount: string
  projectId?: string
}
const formSize = ref<ComponentSize>('default')
const ruleFormRef = ref(null);
const ruleForm = reactive<RuleForm>({
  pmName: '',
  allOrgAddress:[],
  discipline:[],
  province:'',
  city:'',
  county:'',
  validationCategory: '',
  pmType: '',
  academicFieldOne: '',
  academicFieldTwo: '',
  academicFieldThree: '',
  conversionRegion: '',
  totalBudgetWan: '',
  specialFundApplication: '',
  selfFunding: '',
  otherFunding: '',
  timeMsg:[],
  projectStartDate: '',
  projectEndDate: '',
  dwmc: '',
  applyingOrgAddress: '',
  applyingOrgId:'',
  applyingOrgName:'',
  managerName: '',
  applyingDate:'',
  managerGender: '',
  managerBirthDate: '',
  managerIdType: '',
  managerIdNumber: '',
  managerHighestDegree: '',
  managerTitle:'',
  managerPosition: '',
  managerEmail: '',
  managerMobile: '',
  contactName: '',
  contactEmail: '',
  contactPhone: '',
  contactMobile: '',
  contactIdType: '',
  contactIdNumber: '',
  totalParticipants: '',
  seniorTitleCount: '',
  mediumTitleCount: '',
  juniorTitleCount: '',
  otherTitleCount: '',
  doctorDegreeCount: '',
  masterDegreeCount: '',
  bachelorDegreeCount: '',
  otherDegreeCount: '',
})

const formRules = reactive({
})


//邮件
const isIdemail = (rule: any, value: string, callback: (error?: Error) => void) => {
  const reg = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
  if (!value) {
    return callback(new Error('证件号码不能为空'));
  }
  if (!reg.test(value)) {
    return callback(new Error('请输入正确的证件号码'))
  }
  callback();
}

//总数计算
const totalA = ref('');
const totalB = ref('');
const totalNum = (val) => {
  if (val === 0 && nullCs(ruleForm.seniorTitleCount) && nullCs(ruleForm.mediumTitleCount) && nullCs(ruleForm.juniorTitleCount) && nullCs(ruleForm.otherTitleCount)) {
    totalA.value = ruleForm.totalParticipants = (nullChange(ruleForm.seniorTitleCount) + nullChange(ruleForm.mediumTitleCount) + nullChange(ruleForm.juniorTitleCount) + nullChange(ruleForm.otherTitleCount)).toString();
    if (totalA.value !== '' && totalB.value !== '') {
      totalA.value !== totalB.value ? ElMessage.error('职称总人数与学位总人数不相等，请核对数据！') : '';
    }
  } else if (val === 1 && nullCs(ruleForm.doctorDegreeCount) && nullCs(ruleForm.masterDegreeCount) && nullCs(ruleForm.bachelorDegreeCount) && nullCs(ruleForm.otherDegreeCount)) {
    totalB.value = ruleForm.totalParticipants = (nullChange(ruleForm.doctorDegreeCount) + nullChange(ruleForm.masterDegreeCount) + nullChange(ruleForm.bachelorDegreeCount) + nullChange(ruleForm.otherDegreeCount)).toString();
    if (totalA.value !== '' && totalB.value !== '') {
      totalA.value !== totalB.value ? ElMessage.error('职称总人数与学位总人数不相等，请核对数据！') : '';
    }
  }else if(val === 2&&nullCs(ruleForm.specialFundApplication)&&nullCs(ruleForm.selfFunding)&&nullCs(ruleForm.otherFunding)) {
     ruleForm.totalBudgetWan = (nullChange(ruleForm.specialFundApplication) + nullChange(ruleForm.selfFunding) + nullChange(ruleForm.otherFunding) ).toString();
    }
}
//判断空值
const nullCs = (val) => {
  if (val === "" || val === undefined || val === null){
    return false;
  } else{
    return true;
  }
}
//空值为0
const nullChange = (value) => {
  if (value === "" || value === undefined || value === null) {
    return 0;
  } else {
    return Number(value);
  }
}
const rules =reactive<FormRules<RuleForm>>({
  pmName: [{
    required: true,
    message: '项目名称不能为空',
    trigger: 'change',
  }],
  allOrgAddress: [{
    required: true,
    message: '详细地址不能为空!',
    trigger: 'change',
  }],
  discipline: [{
    required: true,
    message: '学科不能为空!',
    trigger: 'change',
  }],
  applyingOrgAddress: [{
    required: true,
    message: '请选择通信地址',
    trigger: 'change',
  }],
  validationCategory: [{
    required: true,
    message: '验证类别不能为空',
    trigger: 'change',
  }],
  pmType: [{
    required: true,
    message: '技术领域不能为空',
    trigger: 'change',
  }],
  conversionRegion: [{
    required: true,
    message: '拟转化地域不能为空',
    trigger: 'change',
  }],
  totalBudgetWan: [{
    required: true,
    validator: decimalNumber,  // 修改为允许小数的验证器
    trigger: 'change',
  }],
  specialFundApplication: [{
    required: true,
    validator: decimalNumber,  // 修改为允许小数的验证器
    trigger: 'change',
  }],
  selfFunding: [{
    required: true,
    validator: decimalNumber,  // 修改为允许小数的验证器
    trigger: 'change',
  }],
  otherFunding: [{
    required: true,
    validator: decimalNumber,  // 修改为允许小数的验证器
    trigger: 'change',
  }],
  timeMsg: [{
    required: true,
    message: '请选择项目周期节点',
    trigger: 'change',
  }],
  projectStartDate: [{
    required: true,
    message: '请选择起始时间',
    trigger: 'change',
  }],
  projectEndDate: [{
    required: true,
    message: '请选择终止时间',
    trigger: 'change',
  }],
  dwmc: [{
    required: true,
    message: '请输入单位名称',
    trigger: 'change',
  }],

  applyingOrgId: [{
    required: true,
    message: '申报单位名称不能为空',
    trigger: 'change',
  }],
  managerName: [{
    required: true,
    message: '项目负责人姓名不能为空',
    trigger: 'change',
  }],
  applyingDate: [{
    required: true,
    message: '申报日期不能为空',
    trigger: 'change',
  }],
  managerGender: [{
    required: true,
    message: '性别不能为空',
    trigger: 'change',
  }],
  managerBirthDate: [{
    required: true,
    message: '请选择出生日期',
    trigger: 'change',
  }],
  managerIdType: [{
    required: true,
    message: '请选择证件类型',
    trigger: 'change',
  }],
  managerIdNumber: [
    { required: true, message: "身份证号不能为空", trigger: "blur" },
    {	validator: validateIDCard, trigger: "blur"}
  ],
  managerHighestDegree: [{
    required: true,
    message: '请选择最高学位',
    trigger: 'change',
  }],
  managerTitle: [{
    required: true,
    message: '请选择职称',
    trigger: 'change',
  }],
  managerPosition: [{
    required: true,
    message: '请输入职务',
    trigger: 'change',
  }],
  managerEmail: [
    { required: true, message: "电子邮箱不能为空", trigger: "change" },
    {	//调用定义的方法校验格式是否正确
      validator: isIdemail, trigger: "blur"
    }
  ],
  managerMobile:  [
    { required: true, message: "移动电话不能为空", trigger: "change" },
    {	//调用定义的方法校验格式是否正确
      validator: validatePhone, trigger: "blur"
    }],
  contactName: [{
    required: true,
    message: '请输入项目联系人姓名',
    trigger: 'change',
  }],
  contactEmail:  [
    { required: true, message: "电子邮箱不能为空", trigger: "change" },
    {	//调用定义的方法校验格式是否正确
      validator: isIdemail, trigger: "blur"
    }
  ],
  contactPhone: [
    { required: true, message: "固定电话不能为空", trigger: "blur" },
    {	//调用定义的方法校验格式是否正确
      validator: validateTelephone, trigger: "blur"
    }],
  contactMobile: [
    { required: true, message: "移动电话不能为空", trigger: "blur" },
    {	//调用定义的方法校验格式是否正确
      validator: validatePhone, trigger: "blur"
    }],
  contactIdType: [{
    required: true,
    message: '请选择证件类型',
    trigger: 'change',
  }],
  contactIdNumber: [
    { required: true, message: "身份证号不能为空", trigger: "blur" },
    {	validator: validateIDCard, trigger: "blur"}
  ],
  totalParticipants: [
    { required: true, validator: checkInputPositiveRealNum, trigger: 'change' },
  ],
  seniorTitleCount: [{
    required: true,
    validator:checkInputPositiveRealNum,
    trigger: 'change',
  }],
  mediumTitleCount: [{
    required: true,
    validator:checkInputPositiveRealNum,
    trigger: 'change',
  }],
  juniorTitleCount: [{
    required: true,
    validator:checkInputPositiveRealNum,
    trigger: 'change',
  }],
  otherTitleCount: [{
    required: true,
    validator:checkInputPositiveRealNum,
    trigger: 'change',
  }],
  doctorDegreeCount: [{
    required: true,
    validator:checkInputPositiveRealNum,
    trigger: 'change',
  }],
  masterDegreeCount: [{
    required: true,
    validator:checkInputPositiveRealNum,
    trigger: 'change',
  }],
  bachelorDegreeCount: [{
    required: true,
    validator:checkInputPositiveRealNum,
    trigger: 'change',
  }],
  otherDegreeCount: [
    {required: true,validator:checkInputPositiveRealNum,trigger: 'change'},
  ],
})


//验证2
interface RuleForm2{
  projectId:string,
  projOverview: string,
}
const placeholderMsg=ref('概述本科技成果的创新概念及研究基础，通过科技创新所形成的核心技术及其应用场景，拟解决的关键问题及对经济社会发展的推动作用，项目技术及资金需求等（限500字以内）。')
const ruleFormRef2 = ref(null);
const ruleForm2 = reactive({
  projectId:'',
  projOverview: "",

})
const rules2 = reactive<FormRules<RuleForm2>>({
  projOverview: [{
    required: true,
    message: '项目简介不能为空',
    trigger: 'blur',
  }]
})

const defaultContent = [
  {
    type: 'paragraph',
    lineHeight: '1.5',
    children: [
      { text: '', fontFamily: '宋体', fontSize: '16px' },
    ]
  },
]
const change = (html: string) => {
}
//验证3
interface RuleForm3{
  projIntroductions:string,
}
const ruleFormRef3 = ref(null);
const ruleForm3 = reactive<RuleForm3>({
  projIntroductions:'<p>概述本科技成果创新点及核心竞争力（请在下方填写）：<br /><br /><br />现有技术基础、知识产权背景（请在下方填写）：<br /><br /><br />拟进一步解决的关键科学技术问题（请在下方填写）：<br /><br /><br />拟解决的产业难点与痛点及其可能产生的市场影响（请在下方填写）：',
})
const rules3 = reactive<FormRules<RuleForm3>>({
  projIntroductions: [{
    required: true,
    message: '项目基本情况介绍不能为空',
    trigger: 'blur',
  }]
})

//验证4
interface RuleForm4{
  projGoalsPlan:string,
}
const placeholderMsg3 = ref('描述本项目科技成果实施概念验证后的预期目标与产业化规划，主要突出以下五项内容：未来成果转化地域和方式；未来形成的产品和目标客户；市场需求及规模，及本项目形成的产品将占有的市场空间；结合未来 3-5 年市场前景预测，描述未来产业规划，包括如何调整技术/产品/服务来迅速占领、拓展市场；根据项目概念验证的时间安排和关键节点，描述未来两年拟完成的产业化规划与目标、关键节点/里程碑（限2000字以内）。');
const ruleFormRef4 = ref(null);
const ruleForm4 = reactive<RuleForm4>({
  projGoalsPlan:'<p>1.未来成果转化地域和方式（请在下方填写）：<br /><br /><br />2.未来形成的产品和目标客户（请在下方填写）：<br /><br /><br />3.市场需求规模及本项目形成产品未来的市场空间：（请在下方填写）：<br /><br /><br />4.结合未来 3-5 年市场前景预测，描述未来产业规划，包括如何调整技术/产品/服务来迅速占领、拓展市场（请在下方填写）：<br /><br /><br />5.根据项目概念验证的时间安排和关键节点，描述未来两年拟完成的产业化规划与目标、关键节点/里程碑（请在下方填写）：',
})
const rules4 = reactive<FormRules<RuleForm4>>({
  projGoalsPlan: [{
    required: true,
    message: '项目基本情况介绍不能为空',
    trigger: 'blur',
  }]
})
//验证5
interface RuleForm5{
  projMaturityAssessment:string,
}
const placeholderMsg4 = ref('根据国家工信部颁布的《技术成熟度等级划分及定义》（见附表），对项目成熟度进行自我评判（限500字以内）。');
const ruleFormRef5 = ref(null);
const ruleForm5 = reactive<RuleForm5>({
  projMaturityAssessment:'',
})
const rules5 = reactive<FormRules<RuleForm5>>({
  projMaturityAssessment: [{
    required: true,
    message: '项目基本情况介绍不能为空',
    trigger: 'blur',
  }]
})
//验证6
interface RuleForm6{
  projSupport:string,
}
const placeholderMsg5 = ref('简要介绍项目团队、资金等已具备的条件；项目所需支持的条件，包括资金、场地、产业资源、其他人员配备等（限1000字以内）。');
const ruleFormRef6 = ref(null);
const ruleForm6 = reactive<RuleForm6>({
  projSupport:'',
})
const rules6 = reactive<FormRules<RuleForm6>>({
  projSupport: [{
    required: true,
    message: '项目基本情况介绍',
    trigger: 'blur',
  }]
})
//验证7
interface RuleForm7{
  projImplPlan:string,
}
const placeholderMsg6 = ref('主要阐述本项目需在哪些方面需进一步验证才能走向产业化，项目潜在风险点及其解决方案；本项目科技成果概念验证的具体内容、实施方案（限3000字以内）。');
const ruleFormRef7 = ref(null);
const ruleForm7 = reactive<RuleForm7>({
  projImplPlan:'',
})
const rules7 = reactive<FormRules<RuleForm7>>({
  projImplPlan: [{
    required: true,
    message: '项目基本情况介绍',
    trigger: 'blur',
  }]
})


//验证8
interface RuleForm8{
  projImplPlan:string,
}
// const placeholderMsg6 = ref('主要阐述本项目需在哪些方面需进一步验证才能走向产业化，项目潜在风险点及其解决方案；本项目科技成果概念验证的具体内容、实施方案（限3000字以内）。');
const ruleFormRef8 = ref(null);
const ruleForm8 = reactive<RuleForm8>({
  projImplPlan: ''
});
const rules8 = reactive<FormRules<RuleForm8>>({
  projImplPlan: [{
    required: true,
    message: '项目基本情况介绍',
    trigger: 'change',
  }]
})
interface RuleFormT8{
  performanceTarget:string,
}
// const placeholderMsg6 = ref('主要阐述本项目需在哪些方面需进一步验证才能走向产业化，项目潜在风险点及其解决方案；本项目科技成果概念验证的具体内容、实施方案（限3000字以内）。');
const ruleFormRefT8 = ref(null);
const ruleFormT8 = reactive<RuleFormT8>({
  performanceTarget:'',
})
const rulesT8 = reactive<FormRules<RuleFormT8>>({
  performanceTarget: [{
    required: true,
    message: '绩效目标不能为空',
    trigger: 'change',
  }]
})




//验证8
interface RuleForm9{
  projectId:string,
  memberName:string,
  gender:string,
  birthdate:string,
  idCardNumber:string,
  technicalTitle:string,
  position:string,
  highestDegree:string,
  major:string,
  fullTimeHoursPerMonth:string,
  workUnit:string,
  mainResponsibilities:string,
  id?: string
}
const ruleFormRef9 = ref<FormInstance>()
const ruleForm9 = reactive<RuleForm9>({
  projectId:"",
  memberName:"",
  gender:"",
  birthdate:"",
  idCardNumber:"",
  technicalTitle:"",
  position:"",
  highestDegree:"",
  major:"",
  fullTimeHoursPerMonth:"",
  workUnit:"",
  mainResponsibilities:"",
})


const rules9 = reactive<FormRules<RuleForm9>>({
  memberName: [{ required: true, message: '请输入机构名称', trigger: 'change' },
  ],
  gender: [{required: true,message: '性别不能为空',trigger: 'change',}],
  birthdate: [{required: true,message: '出生年月不能为空',trigger: 'blur',}],
  idCardNumber: [
    { required: true, message: "身份证号不能为空", trigger: "change" },
    {validator: validateIDCard, trigger: "change"}
  ],
  technicalTitle: [{required: true,message: '专业技术职称不能为空',trigger: 'change',}],
  position: [{required: true,message: '职务不能为空',trigger: 'change',}],
  highestDegree: [{required: true,message: '最高学位不能为空',trigger: 'change',}],
  major: [{required: true,message: '专业不能为空',trigger: 'change',}],
  fullTimeHoursPerMonth:[{ required: true,validator:decimalNumber, trigger: 'change',}],
  workUnit: [{required: true,message: '工作单位不能为空',trigger: 'change',}],
  mainResponsibilities: [{required: true,message: '承担的主要工作不能为空',trigger: 'change',}],
})
//验证8
interface RuleForm10{
  equipmentTotal:string,
  businessTotal:string,
  laborTotal:string,
  indirectTotal:string,
  equipmentSpecial:string,
  equipmentSelf:string,
  equipmentOther:string,
  equipmentBasisNotes: string,
  businessSpecial:string,
  businessSelf:string,
  businessOther:string,
  businessBasisNotes: string,
  laborSpecial:string,
  laborSelf:string,
  laborOther:string,
  laborBasisNotes: string,
  indirectSpecial:string,
  indirectSelf:string,
  indirectOther:string,
  indirectBasisNotes:string,
  specialTotal:string,
  specialSelf:string,
  specialOther:string,
  totalFunding:string,
}
const ruleFormRef10 = ref(null);
const ruleForm10 = reactive<RuleForm10>({
  equipmentTotal:'',
  businessTotal:'',
  laborTotal:'',
  indirectTotal:'',
  equipmentSpecial:'',
  equipmentSelf:'',
  equipmentOther:'',
  equipmentBasisNotes: '',
  businessSpecial:'',
  businessSelf:'',
  businessOther:'',
  businessBasisNotes: '',
  laborSpecial:'',
  laborSelf:'',
  laborOther:'',
  laborBasisNotes: '',
  indirectSpecial:'',
  indirectSelf:'',
  indirectOther:'',
  indirectBasisNotes:'',
  specialTotal:'',
  specialSelf:'',
  specialOther:'',
  totalFunding:'',
})

const rules10 = reactive<FormRules<RuleForm10>>({
  equipmentSpecial: [{ required: true,validator:decimalNumber2, trigger: 'change',}],
  equipmentSelf: [{ required: true, validator:decimalNumber2, trigger: 'change' }],
  equipmentOther: [{ required: true,validator:decimalNumber2, trigger: 'change'}],
  businessSpecial: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  businessSelf: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  businessOther: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  laborSpecial: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  laborSelf: [{ required: true,validator:decimalNumber2,trigger: 'change',}],
  laborOther: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  indirectSpecial: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  indirectSelf: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  indirectOther: [{ required: true, validator:decimalNumber2,trigger: 'change',}],
  equipmentBasisNotes: [{ required: true, message: '设备费依据及说明不能为空', trigger: 'blur' }],
  businessBasisNotes: [{ required: true,  message: '业务费依据及说明不能为空',  trigger: 'change',}],
  laborBasisNotes: [{ required: true, message: '劳务费依据及说明不能为空', trigger: 'change',}],
  indirectBasisNotes: [{ required: true, message: '间接费用依据及说明不能为空', trigger: 'change',}],
  equipmentTotal: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  businessTotal: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  laborTotal: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  indirectTotal: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  specialTotal: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  specialSelf: [{ required: true, validator:decimalNumber2,trigger: 'change',}],
  specialOther: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
  totalFunding: [{ required: true, validator:decimalNumber2, trigger: 'change',}],
})

//验证3
interface RuleFormUser{
  projectId:string,
  orgName:string,
  orgQuality:string,
}


//实时计算（横合计）
const calculateResult = (a,b,c,d) => {
  ruleForm10[d]=(Number(zfcFun(a)) + Number(zfcFun(b)) + Number(zfcFun(c))).toFixed(2)
  return ruleForm10[d];

}
//实时计算（列合计）
const calculateResultLie = (a, b, c, d, e) => {
  ruleForm10[e]=(Number(zfcFun(a)) + Number(zfcFun(b)) + Number(zfcFun(c))+ Number(zfcFun(d))).toFixed(2)
  return ruleForm10[e];
}

// 判断
const zfcFun = (val) => {
  if (val === '' || val === null || val === undefined) {
    return val = 0;
  } else {
    return val;
  }
}
//通用获取数据
const eidtData = ref();
const nullData = ref(false);//判断编辑时是否为空
const t8Msg = ref(false);//
const getEidtData = (id, Api, formData) => {
  const a = ref({ projectId: id,id: id,pageNo:1,pageSize:10});
  Api(toRaw(a.value)).then((res) => {
    if (formData == ruleFormT8) {
      res==null?t8Msg.value=false:(eidtData.value = res, nullData.value = true)
    }else {
      res == null ? nullData.value = false : (eidtData.value = res, nullData.value = true);
    }
    if(res!=null){
      for (const key in formData) {
      if (toRaw(eidtData.value).hasOwnProperty(key)) {
        formData[key] = eidtData.value[key];
      }
    }
    }
  });
}
const getEidtData2 = async (id, Api, formData,val) => {
      // 根据val参数判断是处理数组还是对象
      if(val === 0) {
        // 处理数组数据（如其他参与机构）
        formData.value = []; // 清空现有的数组数据
      }
      
      let a=null;
      let b=null;
      if(val!==0){
        b = await ProjBasicApi.getProjBasic(id);//项目基本信息表
        if(b == null) {
          nullData.value = false;
        } else {
          // 对于reactive对象，使用Object.assign进行赋值
          Object.assign(formData, b);
          nullData.value = true;
        }
      }else{
        a = { projectId: id, pageNo: 1, pageSize: 10 };
        Api(a).then((res) => {
          if(res == null) {
            nullData.value = false;
          } else {
            formData.value = res.list;
            nullData.value = true;
          }
        });
      }
    console.log('formData after assignment:', toRaw(formData), 'val:', val)

}


const router = useRouter();
const submitForm = (val) => {
  if (isButtonDisabled.value === true) return; // 如果按钮已禁用，则直接返回
  isButtonDisabled.value = true; // 禁用按钮
  // 判断第一步获取的projectId
  projectId.value != '' ? eidtMsg.value = true : false;

  if (active.value === 0) {
    projBasicInfoFormRef.value.validate().then(valid => {
      if (valid) {
        // 获取第一步表单数据
        const formData = projBasicInfoFormRef.value.getFormData();

        // 获取其他参与机构数据
        const otherOrgData = projBasicInfoFormRef.value.getOtherOrgData();

        // 验证其他参与机构数据是否有效
        if (!otherOrgData || otherOrgData.length === 0) {
          ElMessage.error('其他参与机构不能为空，请最少录入一条数据！');
          isButtonDisabled.value = false;
          return false;
        }

        // 准备要提交的完整数据
        const submitData = {
          basicInfo: formData,
          otherOrgs: otherOrgData
        };

        // 如果是编辑模式，设置projectId
        if (projectId.value != '') {
          submitData.basicInfo.projectId = projectId.value;
          // 为其他参与机构设置projectId
          submitData.otherOrgs.forEach(item => {
            item.projectId = projectId.value;
          });
        }

        // 一次性提交所有数据
        ProjBasicApi.createProjBasicWithOtherOrg(submitData).then(data => {
          // 如果是新增模式，保存返回的projectId
          if (!projectId.value) {
            projectId.value = data;
          }
          // 成功提示，立即跳转下一步并恢复按钮状态
          ElMessage.success('已保存！');
          if (val < 2) {
            next();
          }
          isButtonDisabled.value = false;
        }).catch(() => {
          isButtonDisabled.value = false;
        });
      } else {
        ElMessage.error('请将表单填写完整！');
        isButtonDisabled.value = false;
        return false;
      }
    });
  } else if (active.value == 1) {
    // 处理第二步表单
    val===1?getEidtData(toRaw(projectId.value), ProjBasicApi.getIntroductions, ruleForm3):'';//编辑时获取数据
    if (nullData.value == false) {
      submitForm2(ruleFormRef2, ProjBasicApi.createOverviewBasic, ruleForm2, val);
      setTimeout(() => {
        val!=1?getEidtData(toRaw(projectId.value),ProjBasicApi.getOverview,ruleForm2):""
      }, 1000);
    } else {
      submitForm2(ruleFormRef2, ProjBasicApi.overviewUpdate, ruleForm2, val)
    }
  } else if (active.value == 2) {
      if (ruleForm3.projIntroductions.replace(/<br\s*\/?>|<p\s*\/?>|<\/p>/g, '')!='') {
         val===1?getEidtData(toRaw(projectId.value), ProjBasicApi.getGoalsPlan, ruleForm4):''
          if (nullData.value == false) {
              submitForm2(ruleFormRef3, ProjBasicApi.createIntroductionsBasic, ruleForm3, val)
              setTimeout(() => {
              val!=1?getEidtData(toRaw(projectId.value), ProjBasicApi.getIntroductions, ruleForm3):''
              }, 1000);
            }else{
              submitForm2(ruleFormRef3, ProjBasicApi.introductionsUpdate, ruleForm3,val)
            }
      } else {
        ElMessage.error('项目基本情况介绍不能为空！');
        isButtonDisabled.value = false;
        return;
      }
  } else if (active.value === 3) {
    if (ruleForm4.projGoalsPlan.replace(/<br\s*\/?>|<p\s*\/?>|<\/p>/g, '') != '') {
      val === 1 ? getEidtData(toRaw(projectId.value), ProjBasicApi.getMaturityAssessment, ruleForm5) : ''
      if(nullData.value == false){
          submitForm2(ruleFormRef4, ProjBasicApi.createGoalsPlanBasic, ruleForm4, val)
          setTimeout(() => {
          val!=1?getEidtData(toRaw(projectId.value), ProjBasicApi.getGoalsPlan, ruleForm4):''
          }, 1000);
      }else{
        submitForm2(ruleFormRef4, ProjBasicApi.goalsPlanUpdate, ruleForm4, val)
      }
    }else {
        ElMessage.error('项目预期目标与产业化规划不能为空！');
      }
  }else if (active.value == 4) {
        val===1?getEidtData(toRaw(projectId.value), ProjBasicApi.getSupport, ruleForm6):""
      if(nullData.value == false){
        submitForm2(ruleFormRef5, ProjBasicApi.createMaturityAssessmentBasic, ruleForm5, val)
         setTimeout(() => {
          val!=1?getEidtData(toRaw(projectId.value), ProjBasicApi.getMaturityAssessment, ruleForm5):''
          }, 1000);
      }else{
        submitForm2(ruleFormRef5, ProjBasicApi.maturityAssessmentUpdate, ruleForm5, val)
      }
        }else if (active.value == 5) {
      val===1?getEidtData(toRaw(projectId.value), ProjBasicApi.getImplPlan, ruleForm7):""
      if(nullData.value == false ){
        submitForm2(ruleFormRef6, ProjBasicApi.createSupportBasic, ruleForm6, val)
         setTimeout(() => {
          val!=1?getEidtData(toRaw(projectId.value), ProjBasicApi.getSupport, ruleForm6):''
          }, 1000);
        }else{
          submitForm2(ruleFormRef6, ProjBasicApi.supportUpdate, ruleForm6, val)
        }
        }else if (active.value === 6) {
      if (ruleForm7.projImplPlan.replace(/<br\s*\/?>|<p\s*\/?>|<\/p>/g, '') != '') {
        val === 1 ? (getEidtData(toRaw(projectId.value), ProjBasicApi.getAssessmentCriteria, arrChildren), getEidtData(toRaw(projectId.value), ProjBasicApi.getGoalsPage, ruleFormT8)) : '';
      if (nullData.value == false) {
        submitForm2(ruleFormRef7, ProjBasicApi.createImplPlanBasic, ruleForm7, val)
          setTimeout(() => {
          val!=1?getEidtData(toRaw(projectId.value), ProjBasicApi.getImplPlan, ruleForm7):""
          }, 1000);
      } else {
          submitForm2(ruleFormRef7, ProjBasicApi.implPlanUpdate, ruleForm7, val)
         }
        ProjBasicApi.tree({ projectId: projectId.value }).then((res) => {
          treeData.value = res;
          arrChildren.value = []; // 清空现有的 arrChildren，防止旧数据干扰
          treeData.value.forEach(itemOne => {
            // 检查 children 是否存在
            if (itemOne.children && itemOne.children.length > 0) {
              itemOne.children.forEach(itemTwo => {
                if (itemTwo.children && itemTwo.children.length > 0) { // 确保有第三级指标
                  itemTwo.children.forEach(itemThree => {
                    // 假设 itemThree 有以下属性：value, sort, indicatorName, assessmentMethod, indicatorScore
                    const newItem: IndicatorItem = {
                      parentId: itemThree.parentId || '',
                      indicatorName: itemThree.label || '',
                      assessmentMethod: itemThree.assessmentMethod || '',
                      indicatorScore: itemThree.indicatorScore || '',
                      value: itemThree.value || '',
                      sort: itemThree.sort,
                      indicatorLevel: itemThree.indicatorLevel || '',
                      projectId: itemThree.projectId || '',
                      id: itemThree.value || ''
                    };
                    arrChildren.value.push(newItem);
                  });
                }
              });
            }
          });
      })
      } else {
        ElMessage.error('项目实施方案不能为空！');
      }
    }else if (active.value == 7) {
    let hasError = false;
    treeData.value.forEach(itemOne => {
      if (itemOne.children) { // 添加检查确保children存在
        itemOne.children.forEach(itemTwo => {
          if (itemTwo.children) { // 确保有第三级指标
            let totalScore = 0;
            arrChildren.value.forEach(item => {
              if (item.parentId === itemTwo.id) {
                totalScore +=  Number(item.indicatorScore);
              }
            });
            // 将 itemTwo.indicatorScore 转换为数字后再比较
            if (Number(itemTwo.indicatorScore) !== totalScore) {
              ElMessage.error(itemTwo.label + '的三级指标分数总和异常请检查修改！');
              hasError = true;
              return;
            }
          }
        });
      }
    });
    if (hasError) {
      // 恢复按钮启用状态
      setTimeout(() => {
        isButtonDisabled.value = false;
      }, 500);
      return;
    }
    if(ruleLength(arrChildren,1)){
      if (nullData.value == false) {
      submitForm2(ruleFormRef8, ProjBasicApi.createAssessmentCriteriaBasic, 'ruleForm8', val)
      setTimeout(() => {
         val!=1?getEidtData2(toRaw(projectId.value), ProjBasicApi.getAssessmentCriteria, arrChildren,0):""
          }, 1000);
    } else {
      submitForm2(ruleFormRef8, ProjBasicApi.createAssessmentCriteriaBasic, 'ruleForm8',val)
    }
    if (t8Msg.value==false) {
      submitForm2(ruleFormRefT8, ProjBasicApi.createEvaluationMetricsBasic, ruleFormT8, val)
       setTimeout(() => {
         val!=1?getEidtData(toRaw(projectId.value), ProjBasicApi.getGoalsPage, ruleFormT8):""
          }, 1000);
      } else {
        submitForm2(ruleFormRefT8, ProjBasicApi.goalsEvaluationMetricsUpdate, ruleFormT8,val)
      }
        getUserLIst();
    }
    }else if (active.value == 9) {
    // 如果不是下一步操作，正常保存数据
    submitForm2(ruleFormRef10, ProjBasicApi.createBudgetExpensesBasic, ruleForm10, val);
      setTimeout(() => {
        getEidtData(toRaw(projectId.value), ProjBasicApi.getBudgetExpenses, ruleForm10);
        isButtonDisabled.value = false;
      }, 1000);
  } else if (active.value == 10) {
      // 处理第十一步：项目附件上传
      if (!attachmentUploadRef.value) {
        setTimeout(() => {
            isButtonDisabled.value = false;
          }, 500);
        return;
      }
      // 设置项目ID
      attachmentUploadRef.value.setProjectId(projectId.value);
      // 如果是下一步或提交，需要验证是否有附件
      if (val === 3) {
        if (!attachmentUploadRef.value.validate()) {
          setTimeout(() => {
            isButtonDisabled.value = false;
          }, 500);
          return;
        }
      }
      // 保存附件
      attachmentUploadRef.value.saveAttachments().then(success => {
        if (success) {
          // 保存成功后，重新加载附件数据
          ProjBasicApi.getMaterials(projectId.value).then(materials => {
            // 如果获取到数据，手动更新附件组件
            if (materials && attachmentUploadRef.value) {
              // 手动设置附件数据
              if (Array.isArray(materials)) {
                attachmentUploadRef.value.setAttachments(materials);
              } else if (materials && materials.list && Array.isArray(materials.list)) {
                attachmentUploadRef.value.setAttachments(materials.list);
              }
            }
            // 根据操作类型执行不同的后续操作
            if (val === 3) {
              // 提交操作：创建审核记录，更新项目状态为1（待单位审核）
              const reviewData = {
                projectId: projectId.value,
                deptId: ruleForm.applyingOrgId || getjgMessage.value?.deptId, // 操作单位ID
                reviewStatus: '1', // 待单位审核
                reviewComments: ''
              };
              
              ProjBasicApi.ReviewRecordsCreate(reviewData).then(() => {
                ElMessage.success('项目提交成功！');
                pushMian();
              }).catch(error => {
                console.error('更新项目状态失败:', error);
                ElMessage.error('提交失败，请重试');
              });
            } else if (val === 2) {
              // 保存操作
              ElMessage.success('项目保存成功！');
            } else {
              // 下一步操作
              next();
            }
          }).catch(error => {
            // 即使获取失败，也继续执行后续操作
            if (val === 3) {
              // 提交操作：创建审核记录，更新项目状态为1（待单位审核）
              const reviewData = {
                projectId: projectId.value,
                deptId: ruleForm.applyingOrgId || getjgMessage.value?.deptId, // 操作单位ID
                reviewStatus: '1', // 待单位审核
                reviewComments: ''
              };
              
              ProjBasicApi.ReviewRecordsCreate(reviewData).then(() => {
                ElMessage.success('项目提交成功！');
                pushMian();
              }).catch(error => {
                console.error('更新项目状态失败:', error);
                ElMessage.error('提交失败，请重试');
              });
            } else if (val === 2) {
              ElMessage.success('项目保存成功！');
            } else {
              next();
            }
          });
        } 
      }).catch((error) => {
        ElMessage.error('保存附件失败，请重试');
      });
    } 
    if (active.value == 8) {
      // 第九步（项目团队基本情况表）
      if (val === 1) {
        // 如果是下一步操作，直接进入下一步
        if (toRaw(tableData.value).length > 0) {
          next();
        } else {
          ElMessage.error('请最少录入一条数据！');
        }
      }
      // 如果不是下一步操作
      if (toRaw(tableData.value).length > 0) {
        getEidtData(toRaw(projectId.value), ProjBasicApi.getBudgetExpenses, ruleForm10);
      } else {
        ElMessage.error('请最少录入一条数据！');
      }
    } 
      // 使用 setTimeout 来在 500ms 后重新启用按钮
      setTimeout(() => {
        isButtonDisabled.value = false;
      }, 500);
}
const ruleFormRefUser = ref(null);
const ruleFormUser = reactive<RuleFormUser>(otherUserArr)
const rulesUser = reactive<FormRules<RuleFormUser>>({
  orgName: [{
    required: true,
    message: '单位名称不能为空',
    trigger: 'change',
  }],
  orgQuality: [
    { required: true, message: '单位性质不能为空', trigger: 'change', },
    { required: true, message: '单位性质不能为空', trigger: 'change', },

  ]
})
const submitOtherUser = async (val,e) => {
  const a = ref({ projectId: toRaw(projectId.value),pageNo:1,pageSize:10});
   ProjBasicApi.getOtherOrg(toRaw(a.value)).then((res) => {
     if (res.list.length != 0) {
    toRaw(otherUserArr.value).filter((item,index)=>{
      res.list.filter((items, indexs) => {
        item.projectId = projectId.value;
      if (res.list[indexs].orgName == otherUserArr.value[index].orgName) {
       otherUserArr.value[index].id = res.list[indexs].id;
        }
      })
  })
   } else {
     toRaw(otherUserArr.value).filter((items, indexs) => {
       if (items.projectId == '') {
         items.projectId = projectId.value;
       }
     })
  }
  ProjBasicApi.createOtherOrgBasic(toRaw(otherUserArr.value))
if (e < 2) {
      next()
    }

  });
}

const pushMian = () => {
        router.push({path:'/projbasic'});
}
const projectId = ref('');
const submitForm2 = (ruleFormRefValue, Api, ruleFormMsg, e) => {
    ruleFormRefValue.value.validate((valid) => {
    if (valid) {
      // 如果是第9步且是下一步操作，特殊处理
      if (active.value === 10 && e === 1) {
        // 在这里不做特殊处理，因为我们已经在submitForm中处理了
      }
      if (active.value == 0) {
        //步骤1选项
        const arr = toRaw(ruleForm.allOrgAddress);
        ruleForm.province = arr[0].toString();
        ruleForm.city = arr[1].toString();
        ruleForm.county = arr[2].toString();
        //处理学科数据
        const dis = toRaw(ruleForm.discipline);
        ruleForm.academicFieldOne = dis[0] ? dis[0].toString() : '';
        ruleForm.academicFieldTwo = dis[1] ? dis[1].toString() : '';
        ruleForm.academicFieldThree = dis[2] ? dis[2].toString() : '';
      }
      if (statementId.value != '') {
        if (ruleFormMsg == 'ruleForm8') {//获取ID
          arrChildren.value.forEach(item => {
            item.projectId = statementId.value; // 设置 projectId
            item.id = item.value; // 将 value 赋值给 id
          })
          Api(toRaw(arrChildren.value))
        } else {
          projectId.value =statementId.value;//获取ID
          ruleFormMsg.projectId = toRaw(projectId.value);
          if (active.value === 0) {
            submitOtherUser(1,e);
          }
          Api(ruleFormMsg)
          // 如果是下一步操作且不是第10步或第0步，则进入下一步
          e < 2 && active.value != 0 && active.value != 10 ? next() : '';
          active.value !== 10 ? ElMessage.success('已修改！') : ElMessage.success('修改已保存！');
          e ===3&&active.value ===10? pushMian() : '';
        }
      } else {//新增提交***********************
              if (active.value === 0 && projectId.value == '') {//获取ID
                Api(ruleFormMsg).then(data => {
                  projectId.value = data;
                  submitOtherUser(0, e);
                  e===2&&active.value != 10 ? ElMessage.success('已保存！') : '';
                });
              } else if (ruleFormMsg == 'ruleForm8') {//获取ID
                arrChildren.value.forEach(item => {
                item.projectId = projectId.value;
                item.id = item.value; // 将 value 赋值给 id
                })
                Api(toRaw(arrChildren.value))
              }else{
              ruleFormMsg.projectId = toRaw(projectId.value);
                Api(toRaw(ruleFormMsg)).then((res) => {
                  (active.value === 0 && projectId.value) != '' ? submitOtherUser(1, e) : '';
                  // 如果是下一步操作且不是第0步或第10步，则进入下一步
                  (active.value === 0 && projectId.value != '') ? '' : (e < 2 && active.value != 10) ? next() : '';
                  e === 2 && active.value <10? ElMessage.success('已保存！') : '';
                  if (active.value == 10&&e===3) {
                    // 提交操作：创建审核记录，更新项目状态为1（待单位审核）
                    const reviewData = {
                      projectId: projectId.value,
                      deptId: ruleForm.applyingOrgId || getjgMessage.value?.deptId, // 操作单位ID
                      reviewStatus: '1', // 待单位审核
                      reviewComments: ''
                    };
                    
                    ProjBasicApi.ReviewRecordsCreate(reviewData).then(() => {
                      ElMessage.success('提交成功！');
                      pushMian();
                    }).catch(error => {
                      console.error('更新项目状态失败:', error);
                      ElMessage.error('提交失败，请重试');
                    });
                  }
              //提示:
                })
              }

      }
      nextMsg.value = true;
    } else {// 这里执行你的提交操作
      ElMessage.error('请将表单填写完整！');
      return false;
    }
  });


}
//验证其他机构与考核指标数据
const ruleLength=(val,e)=>{
  if(e===0){
    if(toRaw(val.value).length>0&&(val.value[0].orgName&&val.value[0].orgQuality)){
      return true;
    }else{
      ElMessage.error('其他参与机构不能为空，请最少录入一条数据！');
      return false;
    }
  }else{
    if(toRaw(val.value).length>0&&(val.value[0].indicatorName&&val.value[0].assessmentMethod&&val.value[0].indicatorScore)){
      return true;
    }else{
      ElMessage.error('指标内容不能为空，请最少录入一条数据！');
      return false;
    }
  }


}

const rttRlue = () => {
  dialogFormVisible.value = false
  ruleFormRef9.value?.resetFields();
}
const resetForm9 = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields();
}

const getjgName = () => {
  getUserProfile().then((res) => {
    ruleForm.applyingOrgName = res.dept.name;
  });//获取基本信息

}

const newFormData = ref([]);
const statementId = ref('');
const eidtMsg = ref(false);

defineExpose({
  ruleForm2,
})
onMounted(() => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');
  ruleForm.applyingDate= `${year}-${month}-${day}`;
});
const getjgMessage = ref();
const getInfoMsg=()=>{
 getUserProfile().then((res) => {
  ruleForm.allOrgAddress.push(Number(res.dept.province),Number(res.dept.city),Number(res.dept.county),)
  ruleForm.applyingOrgName = res.dept.name;
  ruleForm.managerName = res.nickname;
  ruleForm.applyingOrgAddress = res.dept.orgAddress;
    ruleForm.managerIdType = res.idType;
    ruleForm.managerEmail = res.email;
    ruleForm.managerMobile = res.mobile;
    ruleForm.managerIdNumber = res.idNo;
    if (!res.idNo || res.idNo.length !== 18) {
    return null;
  }
    const birthday = `${res.idNo.slice(6, 14)}`;
    const gender = res.idNo[16] % 2 === 0 ? '2' : '1';
    // 转换身份证类型
    const year = birthday.slice(0, 4);
    const month = birthday.slice(4, 6);
    const day = birthday.slice(6, 8);
    ruleForm.managerBirthDate = `${year}-${month}-${day}`;//生日
    ruleForm.managerGender = gender;//性别
    getjgMessage.value = res;

  });//获取基本信息
}
const getList = async () => {
  loading.value = true;
  statementId.value = projectId.value = id;

  //获取编辑数据
  if (statementId.value != '') {
    newFormData.value = await ProjBasicApi.getProjBasic(Number(statementId.value)); // 项目基本信息表
    otherUserArr.value = await ProjBasicApi.getOtherOrg({ projectId: statementId.value, pageNo: 1, pageSize: 10 }); // 其他参与单位

    if (otherUserArr.value == null) {
      otherUserArr.value = [];
      otherUserArr.value.push({ projectId: '', orgName: '', orgQuality: '' });
    } else {
      otherUserArr.value = otherUserArr.value.list;
    }

    for (const key in ruleForm) {
      if (key == 'timeMsg') {
        ruleForm[key].push(toRaw(newFormData.value.projectStartDate), toRaw(newFormData.value.projectEndDate));
      }
      if (key == 'allOrgAddress') {
        ruleForm[key].push(Number(newFormData.value.province), Number(newFormData.value.city), Number(newFormData.value.county));
      }
      if (key == 'discipline') {
        const academicFields = [newFormData.value.academicFieldOne, newFormData.value.academicFieldTwo, newFormData.value.academicFieldThree];
        academicFields.forEach(field => {
          const numField = Number(field);
          // 推送非空和非0的学科领域
          if (field != null && field !== '') {
            ruleForm[key].push(numField);
          }
        });
      }
      if (newFormData.value.hasOwnProperty(key)) {
        ruleForm[key] = newFormData.value[key].toString();
      }
    }

    // 设置子组件ProjBasicInfoForm的数据
    if (projBasicInfoFormRef.value) {
      projBasicInfoFormRef.value.setFormData(newFormData.value);
      projBasicInfoFormRef.value.setOtherOrgData(otherUserArr.value);
    }
    loading.value = false;
  } else {
    getInfoMsg();
    // 如果是新建模式，也初始化子组件
    if (projBasicInfoFormRef.value) {
      projBasicInfoFormRef.value.getInfoMsg();
    }
  }

  loading.value = false;

  // 一、项目简介
}



/** 初始化 **************************/
onMounted(() => {
  getList();
  getjgName();
})

// 修复接口调用时的问题
const submitFormTeamInfo = () => {
  ruleFormRef9.value.validate((valid) => {
    if (valid) {
      if (newMsg.value === 0) {
        ruleForm9.projectId = projectId.value;
        // 使用 @ts-ignore 忽略类型检查，因为API可能需要的参数与接口定义不完全匹配
        // @ts-ignore
        ProjBasicApi.createTeamInfoBasic(ruleForm9).then((res) => {
          rttRlue();
          ElMessage({ message: '新增成功！', type: 'success' });
          getUserLIst();
        })
      } else {
        ruleForm9.id = rowId.value;
        // 使用 @ts-ignore 忽略类型检查
        // @ts-ignore
        ProjBasicApi.teamInfoUpdate(ruleForm9).then((res) => {
          rttRlue();
          ElMessage({ message: '修改成功！', type: 'success' });
          getUserLIst();
        })
      }
    }
  })
}

// 添加获取其他参与单位数据的方法
const getOtherOrgData = async () => {
  if (statementId.value !== '') {
    const result = await ProjBasicApi.getOtherOrg({ projectId: statementId.value, pageNo: 1, pageSize: 10 });
    if (result && result.list) {
      projBasicInfoFormRef.value.setOtherOrgData(result.list);
    }
  }
}

// 提交其他参与单位数据
const submitOtherOrgs = (type, val) => {
  if (!projBasicInfoFormRef.value) return;

  const otherOrgData = projBasicInfoFormRef.value.getOtherOrgData();
  if (otherOrgData && otherOrgData.length > 0 && otherOrgData[0].orgName && otherOrgData[0].orgQuality) {
    if (type === 0) {
      // 新增模式
      otherOrgData.forEach(item => {
        item.projectId = projectId.value;
      });
    }
    ProjBasicApi.otherOrgSubmit(otherOrgData);
  } else if (val === 3) {
    ElMessage.error('其他参与机构不能为空，请最少录入一条数据！');
  }
}

// 添加表单组件引用
const projBasicInfoFormRef = ref();
// 附件上传组件引用
const attachmentUploadRef = ref();

const editForm = (msg,projectId) => {
  // ... 
  router.push({path:'/pmProjBasicForm',query:{id:projectId}});  // 使用ProjBasicForm.vue
}
</script>
<style scoped>
  /* 步骤条样式 */
  .step-header {
    padding: 10px 0 20px 0;
    text-align: center;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 20px;
  }
  
  .step-info {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
  }
  
  .project-steps .step-item {
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .project-steps .step-item:hover {
    transform: translateX(2px);
  }
  
  :deep(.el-step__title) {
    font-size: 15px;
    line-height: 23px;
    font-weight: 500;
  }
  
  :deep(.el-step__title:hover ) {
    color: #409eff;
  }
:deep(.custom-table-header .el-table__header-wrapper tr ) {
  background-color: #8d8888; /* 你想要的表头颜色 */
}
:deep(.el-form-item--default) {
  margin-bottom:0;
}
:deep(.el-form-item) {
  margin-bottom:0;
}
.inp_area :deep(.el-textarea__inner) {
  height:calc(100vh - 310px);
}
:deep(.tableTD  .el-input__inner) {
  display: inline-block;
  width:100px;
}
:deep(.inp_bold  .el-input__inner) {
font-weight: bold;
}
:deep(.inp-wit .el-input__wrapper) {
  width:60px;
}
:deep(.inp-wit .el-input__inner) {
  padding-left:4px;
  text-align: center;
  width:60px;
}
/* 表格视图 */
.tableTD {
  width: 100%;
  margin: 0 auto;
  /*合并多个单元格边框*/
  border-collapse: collapse;
}
.tableTD th {
  color: black;
  font-size: 15px;
  padding:10px;
  border: 1px solid #c0c4cc;
  background-color: #F2F2F2 !important;
}
.tableTD tr td {
  padding: 10px;
  font-size: 15px;
  background-color: #fff;
  border: 1px solid #c0c4cc;
}
.tableTD2{
  border-width:0px;
   border-style:hidden;
  border-collapse: collapse;
}

.tableTD2 th {
  color: #585858;
  font-size: 15px;
  padding:5px;
  border: 1px solid #c0c4cc;
  /* background-color: #F2F2F2 !important; */

}

.tableTD2 tr td{
  padding: 3px;
  font-size: 15px;
  text-align: center;
  background-color: #fff;
  border: 1px solid #c0c4cc;
}
.line-le{
  width:3%;
  margin-right:5px;
  border:1px solid #EFEFEF;
}
.line{
  width:85%;
  margin-top:30px;
  margin-bottom:30px;
  border:1px solid #EFEFEF;
}
.linet{
  width:100%;
  height:20px;
  font-size: 16px;
  font-weight: bold;
  line-height: 20px;
  border-left:3px solid #168FCE;

}
.line2{
  width:78%;
  margin-top:30px;
  margin-bottom:30px;
  border:1px solid #EFEFEF;
}
.line3{
  width:73%;
  margin-top:30px;
  margin-bottom:30px;
  border:1px solid #EFEFEF;
}
.cl_red{
  color:#F56C6C;
  margin-bottom:5px;
  margin-right:3px;
  display: inline-block;
}
.flex_row{
  flex-wrap:wrap;
  align-content:flex-start;
}
.first_1{
  width: 100%;
  display: flex;
  justify-content: center;
  height: calc(100vh - 160px);
}
.tab_7{
  width:100%;
  margin-top:10px;
  height: calc(100vh - 240px);
  padding:10px;overflow: auto;
}
.tab_8{
  width: 100%;
  height: calc(100vh - 350px);
}
.tab_9{
  width:100%;
  overflow: auto;
  margin-top:10px;
  margin-bottom:10px;
  height: calc(100vh - 250px);
}
.fir_card1{
  width: 100%;
  max-width: 1800px;
  height: calc(100vh - 160px);
}
.card_stl{
  width:100%;
  padding:0;
  margin: 0 auto;
  height: calc(100vh - 160px);
}
.div_8_1{
  width:100%;
  overflow: auto;
  margin-top:10px;
  margin-bottom:10px;
  height: calc(100vh - 30px);
}
.size14{
  font-size: 14px;
  color: #939292;
}
.size18{
  font-size: 18px;
  font-weight: bold;
}
.size20{
  color:#F56C6C;
  font-size: 20px;
}
.mt_12{
  margin-top: 12px
}
.h4_1{
  color:#393939;
  display: flex;
  align-items: center;
}
.ml_20{
  margin-left:20px;
}
.bt_next{
  width:90px;
  margin-top: 12px
}
:deep(.centered-input .el-input__inner ) {
  text-align: center;
}

/* 紧凑的步骤卡片样式 */
.compact-steps-card {
  border: 1px solid #e6f7ff;
  border-radius: 8px;
}

.compact-steps-card :deep(.el-card__body ) {
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 简洁的头部 */
.compact-step-header {
  margin-bottom: 12px;
  padding: 10px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #e8f4f8;
}

.step-progress-bar {
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff 0%, #40a9ff 100%);
  border-radius: 4px;
  transition: width 0.4s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.step-info-compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.step-count {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.step-percent {
  font-size: 12px;
  color: #1890ff;
  background: #e6f7ff;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

/* 现代卡片式步骤容器 */
.modern-steps-container {
  flex: 1;
  padding: 6px 0;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 现代步骤项 */
.modern-step-item {
  display: flex;
  align-items: center;
  padding: 7px 10px;
  margin-bottom: 4px;
  background: #ffffff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: default;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
}

.modern-step-item.clickable {
  cursor: pointer;
}

.modern-step-item:hover.clickable {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border-color: #d1d9e0;
}

.modern-step-item.active {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border-color: #1890ff;
  box-shadow: 0 4px 20px rgba(24, 144, 255, 0.15);
}

.modern-step-item.completed {
  background: linear-gradient(135deg, #f6ffed 0%, #f0fff4 100%);
  border-color: #52c41a;
  box-shadow: 0 2px 12px rgba(82, 196, 26, 0.1);
}

/* 步骤序号 */
.step-number {
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  font-size: 11px;
  font-weight: 600;
  color: #999;
  flex-shrink: 0;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.modern-step-item.active .step-number {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-color: #1890ff;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.modern-step-item.completed .step-number {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-color: #52c41a;
  color: white;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.25);
}

.checkmark {
  font-style: normal;
  font-size: 12px;
  font-weight: bold;
}

/* 步骤内容 */
.step-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.step-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  line-height: 1.2;
  margin-bottom: 2px;
  transition: color 0.3s ease;
}

.modern-step-item.active .step-title {
  color: #1890ff;
  font-weight: 600;
}

.modern-step-item.completed .step-title {
  color: #52c41a;
}

.step-status {
  font-size: 11px;
  color: #999;
  padding: 1px 5px;
  border-radius: 6px;
  background: #f5f5f5;
  display: inline-block;
  width: fit-content;
  font-weight: 400;
  transition: all 0.3s ease;
}

.modern-step-item.active .step-status {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.step-status.completed {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

/* 滚动条 */
.modern-steps-container::-webkit-scrollbar {
  width: 4px;
}

.modern-steps-container::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 2px;
}

.modern-steps-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
  transition: background 0.2s ease;
}

.modern-steps-container::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

</style>
<style lang="scss">
:deep(.custom-table-header th ) {
  background-color: #f0f0f0;
  color: #666;
  border-color: #d3dce6;
}
</style>
<style>
::-webkit-scrollbar{
  width: 4px;
  height: 6px;
  margin-left:10px;
}
::-webkit-scrollbar-corner{
  display: block;
}
::-webkit-scrollbar-thumb{
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.2);
}
::-webkit-scrollbar-thumb,
::-webkit-scrollbar-track{
  border-right-color: transparent;
  border-left-color: transparent;
  background-color: rgba(0, 0, 0, 0.1);
}</style>
