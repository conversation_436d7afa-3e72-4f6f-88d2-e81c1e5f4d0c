<template>
    <!-- 搜索工作栏  label-width="68px" -->
    <el-card shadow="never">
      <el-form :model="queryParams" ref="queryFormRef" label-width="auto" >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="项目编码：" prop="projectCode">
            <el-input v-model="queryParams.projectCode" placeholder="请输入" />
          </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="项目名称：" prop="pmName">
            <el-input v-model="queryParams.pmName" placeholder="请输入" />
          </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="项目类别：" prop="pmType">
              <el-select v-model="queryParams.pmType" placeholder="请选择" >
                <el-option v-for="item in getStrDictOptions(DICT_TYPE.PM_TYPE)" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
           <el-col :span="6">
            <el-form-item label="负责人：" prop="contactName">
            <el-input v-model="queryParams.contactName" placeholder="请输入" />
          </el-form-item>
          </el-col>
         <el-col :span="6">
          <el-form-item label="申报单位：" prop="applyingOrgName">
            <el-input v-model="queryParams.applyingOrgName" placeholder="请输入" />
          </el-form-item>
          </el-col>
          <el-col :span="6">
          <el-form-item label="申报日期：" prop="applyingDate">
            <el-input v-model="queryParams.applyingDate" placeholder="请输入" />
          </el-form-item>
          </el-col>
           <el-col :span="6">
            <el-form-item label="项目状态：" prop="projectStatus">
            <el-select v-model="queryParams.projectStatus" placeholder="请选择" >
              <el-option v-for="item in xmStateData" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          </el-col>
<!--          <el-col :span="6">
            <el-form-item label="形式审查状态：" prop="xssczt">
            <el-select v-model="queryParams.xssczt" placeholder="请选择">
              <el-option label="未开始" value="0" />
              <el-option label="进行中" value="1" />
              <el-option label="已结束" value="2" />
            </el-select>
          </el-form-item>
          </el-col>-->
          <el-col :span="6">
            <el-form-item label="是否暂存退回原因：" prop="zcth">
            <el-select v-model="queryParams.zcth" placeholder="请选择">
              <el-option label="是" value="0" />
              <el-option label="否" value="1" />
            </el-select>
          </el-form-item>
          </el-col>
          <el-col :span="24" style="display: flex; justify-content: flex-end;">
            <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
            <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
            <el-button type="success" plain @click="handleExport" :loading="exportLoading" v-hasPermi="['pm:proj-basic:export']">
              <Icon icon="ep:download" class="mr-5px" /> 导出
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  <!-- 列表 -->
    <el-card shadow="never" class="mt-3">
      <div class="mb-2.5">
      <el-button class="ml-3" type="primary" v-show="isReminders" plain @click="openForm('create')" v-hasPermi="['pm:proj-basic:create']"><el-icon :size="14" ><Plus /></el-icon>项目申报</el-button>
      <el-button class="ml-3" type="primary" v-show="!isReminders" plain @click="navigateToForm" v-hasPermi="['pm:proj-basic:create']"><el-icon :size="14" ><Plus /></el-icon>项目申报</el-button>
      <el-button class="ml-3" type="primary" plain @click="proExport" :loading="exportDeclarationLoading"><el-icon :size="14" ><Download /></el-icon>导出申报书</el-button>
      <el-button class="ml-3" type="primary" plain @click="passFun2('9')" :disabled="disBtn" v-hasPermi="['pm:review-records:medical']">批量受理</el-button>
      <el-button class="ml-3" type="primary" plain @click="passFun2('6')" :disabled="disBtn" v-hasPermi="['pm:review-records:medical']"><el-icon class="mr-1" :size="14"><Check /></el-icon>批量审查通过</el-button>
      <el-button class="ml-3" type="danger" plain @click="passFun2('5')" :disabled="disBtn" v-hasPermi="['pm:review-records:medical']"><el-icon class="mr-1" :size="14"><CloseBold /></el-icon>批量不通过</el-button>
      <el-button class="ml-3" type="danger" plain @click="passFun2('7')" :disabled="disBtn" v-hasPermi="['pm:review-records:medical']"><el-icon class="mr-1" :size="14"><CloseBold /></el-icon>批量退回</el-button>
      <el-button class="ml-3" type="success" plain @click="passFun2('8')" :disabled="disBtn" v-hasPermi="['pm:review-records:medical']"><el-icon class="mr-1" :size="14"><CircleCheckFilled /></el-icon>批量立项</el-button>
    </div>
      <el-table
        v-loading="loading"
        :data="list"
        max-height="500px"
        border
        ref="tableRef"
        row-key="projectId"
        @selection-change="handlechange"
        stripe
        :header-cell-style="{ background: '#f6f6f6',color: '#656565', fontWeight: 'bold'}"
        style="width: 100%;">
        <el-table-column type="selection"  :reserve-selection="true" width="50"  align="center" />
        <el-table-column v-if="lists[1].ispass" label="项目编号" property="projectCode" align="center" />
        <el-table-column v-if="lists[2].ispass" label="申报年份"  align="center" >
          <template #default="scope">
            {{ new Date(scope.row.applyingDate).getFullYear() }}
          </template>
        </el-table-column>
        <el-table-column v-if="lists[3].ispass" label="项目名称" property="pmName" show-overflow-tooltip/>
        <el-table-column v-if="lists[4].ispass" label="申报单位" property="applyingOrgName" show-overflow-tooltip/>
        <el-table-column v-if="lists[5].ispass" label="负责人" align="center" prop="managerName" >
          <template #default="scope">{{ scope.row.managerName }}</template>
        </el-table-column>
        <el-table-column v-if="lists[6].ispass" label="项目状态" property="projectStatus" align="center" >
          <template #default="scope">
            <el-tag class="tag_sty" v-show='scope.row.projectStatus==item.value' v-for="item in xmStateData" :key="item.value" plain :type="scope.row.projectStatus===8?'success':isShowBtn3(scope.row.projectStatus)?'warning':'primary'">{{item.label }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column v-if="lists[7].ispass" label="日志" property="date" align="center" >
          <!-- @click="viewDetails" -->
          <template #default="scope"><el-button link type="primary" @click="openDrawer( scope.row)">查看</el-button></template>
        </el-table-column>
        <el-table-column v-if="lists[8].ispass" label="操作" property="address" align="center" fixed="right" width="260">
          <template #header>
            <el-tooltip class="box-item" effect="dark" content="列设置" placement="top">
              <el-dropdown  placement="bottom-start" trigger="click">
                <el-button link ><span class="cz_sty">操作</span><el-icon :size="20" style="margin-left: 5px;"><Setting /></el-icon></el-button>
              <!-- table列操作 -->
                <template #dropdown>
                <el-dropdown-menu >
                  <el-scrollbar class="scrollbar_pd">
                    <el-checkbox-group v-model="check">
                      <div v-for="(item,index) in checkList" :key="index">
                        <el-checkbox v-show="index<(checkList.length-1)" :value="item" :key="item" @change="changeAll(item)">{{ item }}</el-checkbox>
                      </div>
                    </el-checkbox-group>
                    <div style="width:100%;text-align:center">
                      <el-button type="primary" @click="saveCheckMsg" style="margin-top: 10px;" >确定</el-button>
                    </div>
                  </el-scrollbar>
                  </el-dropdown-menu>
              </template>
              </el-dropdown>
            </el-tooltip>
          </template>
          <!-- 操作 状态-->
          <template #default="scope">
              <el-button link class="blue_text" @click="previewForm('update', scope.row.projectId)">预览</el-button>
              <!-- 直接显示操作按钮 -->
              <el-button v-show="isShowBtn(scope.row.projectStatus)" link type="primary" v-hasPermi="['pm:proj-basic:update']" @click="editForm('update', scope.row.projectId)">编辑</el-button>
              <!-- 一级 -->
              <el-button v-show="isShowBtn(scope.row.projectStatus)" link type="primary" v-hasPermi="['pm:review-records:stair']" @click="passFun( scope.row,1,nopassconst1,nopass1)">提交</el-button>
              <el-button v-show="scope.row.projectStatus === 1" type="danger" link v-hasPermi="['pm:review-records:stair']" @click="passFun( scope.row,2,nopassconst0,nopass0)">撤回申请</el-button>
              <el-button v-show="isShowBtn(scope.row.projectStatus)" class="red_text" type="danger" link v-hasPermi="['pm:proj-basic:delete']" @click="handleDelete(scope.row.projectId)">删除</el-button>
              <!-- 二级 -->
              <el-button v-show="noPassFun(scope.row.projectStatus)" class="blue_text" link type="primary" v-hasPermi="['pm:review-records:secondlevel']" @click="backFun( scope.row,3,nopassconst3,nopass3)">审核通过</el-button>
              <el-button v-show="isShowdwTh(scope.row.projectStatus)" link type="danger" v-hasPermi="['pm:review-records:secondlevel']" @click="backFun( scope.row,4,nopassconst4,nopass4)">单位退回</el-button>
              <!-- 三级 -->
              <el-button v-show="isAdmissibility(scope.row.projectStatus)" class="blue_text" link type="primary" v-hasPermi="['pm:review-records:threelevel']" @click="passFun( scope.row,9,nopassconst9,nopass9)" >受理</el-button>
              <el-button v-show="isShowdwXsPass(scope.row.projectStatus)" class="blue_text" link type="primary" v-hasPermi="['pm:review-records:threelevel']" @click="passFun( scope.row,6,nopassconst6,nopass3)" >审查通过</el-button>
              <el-button v-show="isShowdwXsPass(scope.row.projectStatus)" link type="danger" v-hasPermi="['pm:review-records:threelevel']"  @click="backFun( scope.row,5,nopassconst5,nopass5)">不通过</el-button>
              <el-button v-show="isShowdwXsPass(scope.row.projectStatus)" link type="danger" v-hasPermi="['pm:review-records:threelevel']" @click="backFun( scope.row,7,nopassconst7,nopass4)">退回</el-button>
              <el-button v-show="isShowLx(scope.row.projectStatus)" class="green_text" link type="success" v-hasPermi="['pm:review-records:threelevel']" @click="passFun( scope.row,8,nopassconst8,nopass8)">立项</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize" @pagination="getList" />
       <el-dialog v-model="dialogFormVisible" :title="backTit" style="top:-8vh;width:50%"  @close="handleDialogClose">
            <div>
              <div v-show="backMsgTit!==3" class="mb-5 pl-5 mt-6 linet">申报须知</div>
            <div v-show="backMsgTit===3" class="mb-5 pl-5 mt-6 linet2">申报须知</div>
            <p class="mb-5 ml-5">
              1. 申报书的内容将作为项目评审以及签订目标任务书的重要依据，申报书的各项填报内容须实事求是、准确完整、层次清晰。<br />
              2．请申报单位和申报人认真阅读申报通知，所申报的项目研究内容须符合通知要求。<br />
              3．项目名称应清晰、准确反映研究内容，项目名称不宜宽泛。<br />
              4．申报书标题统一用黑体四号字，申报书正文部分统一用宋体小四号字填写。正文（包括标题）行距为1.5倍。凡不填写的内容，请用"无"表示。<br />
              5．外来语要同时用原文和中文表达，第一次出现的缩略词，须注明全称。<br />
              6．申报书中的单位名称，请填写全称，并与单位公章一致。<br />
              7. 验证类别包括：原理验证、产品与场景体系验证、原型制备与技术可行性验证、商业前景验证、其他等类别。<br />
              8.技术领域：按照国家学科分类有关标准填写，一般填写至三级学科。<br />
            </p>
            <div v-show="backMsgTit===3" class="mb-5 pl-5 linet2">申请单位意见</div>
           <el-form v-show="backMsgTit!==3" ref="backRef" style="width:100%;" :model="ruleFormBack" :rules="rules" label-width="auto" >
            <el-form-item :label="backMsgTit===3?'':'驳回原因'" prop="reviewComments">
              <el-input v-model="ruleFormBack.reviewComments" type="textarea" :rows="3" />
            </el-form-item>
          </el-form>
          <el-form v-show="backMsgTit===3" ref="backRef2" style="width:100%;" :model="ruleFormBack2" :rules="rules2" label-width="auto" >
            <!-- <el-form-item label="" prop="reviewComments">
              <el-input v-model="ruleFormBack2.reviewComments" type="textarea" :rows="3" />
            </el-form-item> -->
            <el-row>
              <el-col :span="10" class="ml-5">
                <el-form-item label="" >
                    <el-button class="primary-btn" type="success" plain @click="downloadPDF">下载PDF模板</el-button>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item prop="filePath">
                <el-upload ref="uploadRef" :data="data" class="upload-demo" :action="uploadUrl" :auto-upload="false" v-model:file-list="fileList" :limit="1" :on-exceed="handleExceed" :on-change="handleFileChange" :on-error="submitFormError" :on-success="submitFormSuccess" :http-request="httpRequest" accept=".pdf">
                  <template #trigger>
                    <el-button type="primary">上传</el-button>
                  </template>
                  <template #tip>
                  <div class="el-upload__tip">请上传pdf扫描件，10M以内</div>
                  </template>
                </el-upload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
            </div>
          <template #footer>
            <div class="dialog-footer">
              <el-button @click="handleCancel">取 消</el-button>
              <el-button type="primary" v-show="backMsgTit!==3&&backMsgTit3===0"  @click="submitFormBack(backRef)">确 定</el-button>
              <el-button type="primary" v-show="backMsgTit===3&&backMsgTit3===0"  @click="submitFormBack(backRef2)">确 定</el-button>
              <el-button type="primary" v-show="backMsgTit3===1"  @click="submitFormBack2()">确 定</el-button>
            </div>
          </template>
        </el-dialog>


      <!-- 表单弹窗：添加/修改 -->
      <dialoghint ref="formRef"  :formLoading="formLoading" @success="getList" />
      <drawerview ref="drawerRef" :drawerOpen="drawerOpen" @success="getList" />
    </el-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import download from '@/utils/download'
import {useRouter} from 'vue-router'; //路由
import dialoghint from './dialoghint.vue'
import drawerview from './drawerview.vue'
import {useUpload} from "@/components/UploadFile/src/useUpload";
import {ReviewRecordsApi} from '@/api/pm/reviewrecords'
import type {
  FormInstance,
  FormRules,
  UploadInstance,
  UploadProps,
  UploadRawFile
} from 'element-plus'
import { getUserProfile } from '@/api/system/user/profile'
import {ElMessage, ElMessageBox, genFileId} from 'element-plus'
import {ProjBasicApi, ProjBasicVO} from '@/api/pm/projbasic'
import {DICT_TYPE, getStrDictOptions} from '@/utils/dict'
import * as ConfigApi from '@/api/infra/config'
import {Plus} from "@element-plus/icons-vue";

const { uploadUrl, httpRequest } = useUpload()

/** 成果概念验证项目基本信息 列表 */
defineOptions({ name: 'ProjBasic' })
const fileList = ref([]) // 文件列表
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const formLoading = ref(true);
const drawerOpen = ref(false);
const backTit = ref('');
const selectMsg=ref('');//批量处理
const selectArr=ref<number[]>([]);
const backMsgTit = ref();
const backMsgTit3 = ref(0);
const disBtn=ref(true);
const data = ref({ path: '' });
const dialogFormVisible=ref(false);//退回弹窗
const isReminders=ref(false);//退回弹窗
const isReportingRestrictions=ref(false);//退回弹窗
const loading = ref(true) // 列表的加载中
const list = ref<ProjBasicVO[]>([]) // 列表的数据
// const list =ref([{managerName:'小王',academicFieldOne:'生物',academicFieldTwo:'细胞生物学',academicFieldThree:'细胞结构与形态',applyingOrgId:'张仲景医院',projectState:'未提交',validationCategory:'已结束'}])
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  projectCode: '',
  applyingDate: '',
  applyingOrgName:'',
  xmcontactNamesb: '',
  pmName: '',
  contactName:'',
  pmType:'',
  projectStatus: '',
  xmstate: '',
  sbdw: '',
  xssczt: '',
  zcth: '',
})

const getConfig = async () => {
  const {value} = await ConfigApi.getConfig(21)
  isReminders.value = (value === 'true');
  const islimit = await ConfigApi.getConfig(22)
  if(islimit.value === 'true'){
    isReportingRestrictions.value = await ProjBasicApi.getReportingRestrictions();
    console.log(isReportingRestrictions.value,'isReportingRestrictions')
  }else{
    isReportingRestrictions.value = true;
  }
}
const navigateToForm = () => {
  if (!isReportingRestrictions.value) {
    message.error('当前年度已达到申报上限，无法进行项目申报！')
    return
  }
  router.push({ path: '/pmProjBasicForm', query: { id: '' } });
};

const tableRef = ref<any>()

const handlechange = (val: any[]) => {
  val.length!==0?disBtn.value=false:disBtn.value=true;
  // 保存当前选择的数据
  selectArr.value = [];
  toRaw(val).forEach((item)=>{
    let numId=Number(item.projectId);
    selectArr.value.push(numId);
  })
}
/** 处理上传的文件发生变化 */
const handleFileChange = (file) => {
  data.value.path = ruleFormBack2.path = file.name;
  referQuery.value.filePath = uploadUrl;
}


const uploadRef = ref<UploadInstance>()
const handleExceed: UploadProps['onExceed'] = (files) => {
  uploadRef.value!.clearFiles()
  const file = files[0] as UploadRawFile
  file.uid = genFileId()
  uploadRef.value!.handleStart(file)
}
const handleDialogClose = () => {
  uploadRef.value?.clearFiles()
}
const  handleCancel = () => {
  // 关闭对话框
  dialogFormVisible.value = false;
  // 清除上传的文件列表
  uploadRef.value?.clearFiles()
}
/** 上传错误提示 */
const submitFormError = (): void => {
  message.error('上传失败，请您重新上传！')
  formLoading.value = false
}
/** 文件上传成功处理 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitFormSuccess = (file) => {
  // 清理
  unref(uploadRef)?.clearFiles()
  // 提示成功，并刷新
  message.success(t('common.createSuccess'))
  emit('success')
}

const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const exportDeclarationLoading = ref(false) // 导出申报书的加载中
//动态列表
const check=ref(['全选','项目编号','申报年份','项目名称','申报单位','负责人','项目状态','日志','操作'])
const checkList=ref(['全选','项目编号','申报年份','项目名称','申报单位','负责人','项目状态','日志','操作'])
const lists = ref([
  { label: '全选', ispass: true },
  { label: '项目编号', ispass: true },
  { label: '申报年份', ispass: true },
  { label: '项目名称', ispass: true },
  { label: '申报单位', ispass: true },
  { label: '负责人', ispass: true },
  { label: '项目状态', ispass: true },
  { label: '日志', ispass: true },
  { label: '操作', ispass: true },
])
const saveCheckMsg=(()=>{
  loading.value=true;
  // 判断其他
  setTimeout(() => {
    lists.value.map(i => {
    if (check.value.includes(i.label)) {
      i.ispass = true;
    } else {
      i.ispass = false;
    }
      loading.value=false;
  })
  }, 1000);
})
// 全选判断
const changeAll=((e)=>{
  console.log(e,'e')
  if(e==='全选'){
    if(check.value.includes('全选')){
    check.value=JSON.parse(JSON.stringify(checkList.value));
    }else{
      check.value=['操作'];
    }
  }
})
const xmStateData = ref(getStrDictOptions(DICT_TYPE.PROJ_STATUS));
//省级联动
/** 查询列表 */
const getList = async () => {
  loading.value = true;
  try {
    const data = await ProjBasicApi.getProjBasicPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false;
  }
}
/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}



/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  if (!isReportingRestrictions.value) {
    message.error('当前年度已达到申报上限，无法进行项目申报！')
    return
  }
  formRef.value.open(type, id)
}

const drawerRef = ref()
const openDrawer = (type: string, id?: number) => {
  drawerRef.value.open(type, id)
}
//编辑
 const router = useRouter();
const editForm = (msg,projectId) => {
   ElMessageBox.confirm(
     '是否确定编辑此数据?',
    '提示:',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      ElMessage({ type: 'success', message: '请编辑...', });
      router.push({path:'/pmProjBasicForm',query:{id:projectId}});
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消' });
    })
}
//退回显示
const isShowBtn = (e) => {
  if (e === 0 || e === 2 || e === 4 || e === 7) {
    return true;
  } else {
    return false;
  }
}
//回显状态
const isShowBtn3 = (e) => {
  if (e === 2 || e === 4 || e === 7 || e === 5) {
    return true;
  } else {
    return false;
  }
}
// 单位审核通过
const noPassFun = (e) => {
  if (e === 1) {
    return true;
  }else{
    return false;
  }
}
//单位审核通过退回
const isShowdwTh = (e) => {
  if (e === 1) {
    return true;
  }else{
    return false;
  }
}
// 形式审查通过 不通过形式
const isShowdwXsPass = (e) => {
  if (e===9) {
    return true;
  }else{
    return false;
  }
}
// 形式审查通过 受理待确认状态
const isAdmissibility = (e) => {
  if (e===3) {
    return true;
  }else{
    return false;
  }
}
//
const isShowLx = (e) => {
  if (e===6) {
    return true;
  }else{
    return false;
  }
}

interface RuleFormBack {
  reviewComments: string
}
const backRef = ref<FormInstance>()
const ruleFormBack = reactive<RuleFormBack>({
  reviewComments: '',
})
const rules = reactive<FormRules<RuleFormBack>>({
  reviewComments: [
    { required: true, message: '驳回原因不能为空', trigger: 'blur' }
  ]})
//申请部门
  interface RuleFormBack2 {
    reviewComments: string
    path: string
    filePath:string
}
const backRef2 = ref<FormInstance>()
const ruleFormBack2 = reactive<RuleFormBack2>({
  reviewComments: '',
  path: '',
  filePath:''
})
const rules2 = reactive<FormRules<RuleFormBack2>>({
  reviewComments: [
    { required: true, message: '驳回原因不能为空', trigger: 'blur' }
  ]})

//退回申请参数
const referQuery = ref<Partial<{
  id: number, // 主键ID
  projectId: number, // 项目主键
  deptId: number | string, // 操作单位
  operationUser: string, // 操作用户
  filePath: string, // 操作用户
  path: string, // 用于上传
  reviewStatus: string | number, // 审核状态
  reviewComments: string, // 审核意见
  fileId?: number // 文件ID
}>>({
  projectId: undefined,
  deptId: '1',
  filePath: '',
  path: '',
  reviewStatus: '',
  reviewComments: '',
});
const nopassconst1=ref("是否确认提交审核？")
const nopass1 = ref("提交成功！")
const nopassconst0=ref("是否确认撤回申请？")
const nopass0 = ref("撤回成功！")
const nopassconst6=ref("是否确认形式审查通过？")
const nopassconst3=ref("是否确认单位审核通过？")
const nopass3 = ref("已通过！")
const nopassconst5=ref("是否确认形式审查不通过？")
const nopass5 = ref("操作成功！")
const nopassconst8=ref("是否确认立项？")
const nopass8=ref("已立项！")
const nopassconst4 = ref("是否确认退回申请？")
const nopassconst7 = ref("是否确认退回？")
const nopass4 = ref("已退回！")
const nopassconst9 = ref("是否确认受理？")
const nopass9 = ref("操作成功！")

// 定义 backMsg 的具体类型
interface BackMsgType {
  row: {
    projectId?: number;
    applyingOrgId?: number;
    [key: string]: any;
  };
  val: string;
  Msg: string;
  Mg: string;
}

const backMsg = ref<BackMsgType>({
  row: {},
  val: '',
  Msg: '',
  Mg: ''
});

const backFun = (row, val, Msg, Mg) => {
  backMsg.value.row = row;
  backMsg.value.val = val;
  backMsg.value.Msg = Msg;
  backMsg.value.Mg = Mg;
  dialogFormVisible.value = true;
  backMsgTit.value = val;
  val === 5 ? backTit.value = '形式审查不通过' : val === 3 ? backTit.value="申请单位意见" :backTit.value= '退回';
}
// 审核提交校验最后一步
const endMsg = ref(0);
const endData = ref();
const endRules = ref({equipmentTotal:'',
  businessTotal:'',
  laborTotal:'',
  utilitiesTotal:'',
  testingTotal:'',
  performanceTotal:'',
  indirectTotal:'',
  equipmentSpecial:'',
  equipmentSelf:'',
  equipmentOther:'',
  equipmentBasisNotes: '',
  businessSpecial:'',
  businessSelf:'',
  businessOther:'',
  businessBasisNotes: '',
  laborSpecial:'',
  laborSelf:'',
  laborOther:'',
  laborBasisNotes: '',
  utilitiesSpecial:'',
  utilitiesSelf:'',
  utilitiesOther:'',
  utilitiesBasisNotes: '',
  testingSpecial:'',
  testingSelf:'',
  testingOther:'',
  testingBasisNotes: '',
  performanceSpecial:'',
  performanceSelf:'',
  performanceOther:'',
  performanceBasisNotes: '',
  indirectOtherSpecial:'',
  indirectOtherSelf:'',
  indirectOther:'',
  indirectOtherDescription:'',
  specialTotal:'',
  specialSelf:'',
  specialOther:'',
  totalFunding:'',});

 // 单位审批通过
const passFun = async(row, val, Msg, Mg) => {
   if (row.projectStatus === 0) {
    await ProjBasicApi.getBudgetExpenses({ projectId: row.projectId, id: row.projectId, }).then((res) => {
      endData.value = res;
      if (toRaw(endData.value) === null) {
        return endMsg.value = 1;
      } else {
        for (const key in toRaw(endRules.value)) {
        if (toRaw(endData.value).hasOwnProperty(key)) { // 检查对象 b 是否有相同的键
          if (!toRaw(endData.value)[key]) { // 检查对象 a 中该键的值是否为空
            endMsg.value = 1;
            break; // 终止循环
          } else {
            endMsg.value = 0;
          }
        }
      }
      }
    })
  }
  if (endMsg.value === 1) {
     ElMessageBox.alert(
    '项目申报填写完整后方可提交！',
    '提示！',
    {
      confirmButtonText: '确 认',
      type: 'warning',
    }
  )
  } else {
    ElMessageBox.confirm(
    Msg,
    '提示！',
    {
      confirmButtonText: '确 认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
      // 构造符合 ReviewRecordsVO 的对象
      const reviewData = {
        id: 0,
        projectId: Number(row.projectId),
        deptId: Number(row.applyingOrgId),
        operationUser: '', 
        filePath: '',
        fileId: 0,
        reviewStatus: String(val),
        reviewComments: ''
      };
      ReviewRecordsApi.createReviewRecords(reviewData).then((res) => {
        getList();
        ElMessage({ type: 'success', message: Mg, });
      })
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消' })
    })
  }
}
// 批量处理selectArr  selectMsg
const passFun2 = (val) => {
  selectMsg.value=val;//赋值
  selectFun();
}
//判断是否显示退回弹窗
let backNum=ref(false);
let promptCon=ref('');
let promptCon2=ref('');
const selectFun=()=>{
  // 过滤
  let newArr: any[] = [];
  // 循环出被选中数据
  list.value.map((item)=>{
    selectArr.value.map((items)=>{
      if(item.projectId==items){
        newArr.push(item);
      }
    })
  });
  //提示
  if(!groupByStatus(1,newArr,selectMsg)){
    backMsgTit3.value=1;
    backMsgTit.value='';
    dialogFormVisible.value = backNum.value;
    if(!backNum.value){
      backMsgTit3.value=0;
      ElMessageBox.confirm(
      promptCon.value,
    '提示！',
    {
      confirmButtonText: '确 认',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    getUserProfile().then((res) => {
      // 构造符合 API 要求的参数
      const batchData = {
        id: 0,
        projectId: 0,
        deptId: Number(res.dept.id),
        operationUser: '',
        filePath: '',
        fileId: 0,
        reviewStatus: selectMsg.value,
        reviewComments: ruleFormBack.reviewComments,
        projectIds: toRaw(selectArr.value),
      };
      // 使用 @ts-ignore 跳过类型检查，因为API支持额外的 projectIds 参数
      // @ts-ignore
      ReviewRecordsApi.createBatch(batchData).then((res)=>{
        dialogFormVisible.value = false;
        getList();
        })
      })
    })
    .catch(() => {
      ElMessage({ type: 'info', message: '已取消' })
    })
    }
  }else{
    ElMessage({ showClose: true,type: 'error', message: '操作失败！选项中含有不符合要求的信息！' })
  }
}
    // 判断状态进行分组
    const groupByStatus=(num,arr,msg)=>{
      if(num===1){
        let resultMsg='';
        if(msg.value==='5'){//不通过
          resultMsg='9';
          backNum.value=true;
        }else if(msg.value==='6'){//审查通过医科院级（最高）
          resultMsg='9';
          promptCon2.value=nopass3.value;
          promptCon.value=nopassconst6.value;
        }else if(msg.value==='7'){//退回
          resultMsg='9';
          backNum.value=true;
        }else if(msg.value==='8'){//立项
          resultMsg='6';
          promptCon2.value=nopass8.value;
          promptCon.value=nopassconst8.value;
        }else if(msg.value==='9'){//立项
          resultMsg='3';
          promptCon2.value=nopassconst9.value;
          promptCon.value=nopassconst9.value;
        }
        // 显示退回弹窗
        msg.value ==='5'?backTit.value = '形式审查不通过':msg.value ==='7'?backTit.value= '退回':'';
        if(arr.length===0){
          return true;
        }else{
          console.log(resultMsg,'resultMsg')
          console.log(arr.some(item=>item.projectStatus!=resultMsg),'arr')
           return arr.some(item=>item.projectStatus!=resultMsg);
        }
      }
    }



//预览
    const previewForm = (msg,projectId) => {
      router.push({path:'/preview',query:{id:projectId}});
    }
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ProjBasicApi.deleteProjBasic(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProjBasicApi.exportProjBasic(queryParams)
    download.excel(data, '成果概念验证项目基本信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 导出按钮操作 */
const proExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportDeclarationLoading.value = true
    // 获取选中的行数据的 id 列表
    if (selectArr.value.length === 0) {
      message.error('请至少选择一个项目');
      return;
    }
    const projectIds = selectArr.value.join(',');
    const data = await ProjBasicApi.exportProjBasicWord({ projectIds: projectIds })
    download.zip(data, '成果概念验证项目申报书.zip')
  } catch {
  } finally {
    exportDeclarationLoading.value = false
  }
}
const localFileName = '单位-部门意见.pdf';  // 这里替换为你的本地文件名
const filePath = `/${localFileName}`;  // 假设文件位于 public 目录下
const downloadPDF = () => {
  const link = document.createElement('a');
  link.href = filePath;
  link.download = localFileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}


/** 初始化 **************************/
onMounted(() => {
  getList()
  getConfig()
})

// 退回申请参数
const submitFormBack = async (formEl: FormInstance | undefined) => {
  referQuery.value.path = ruleFormBack2.path;
  backMsgTit.value === 3 ? referQuery.value.reviewComments = ruleFormBack2.reviewComments : referQuery.value.reviewComments = ruleFormBack.reviewComments;
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      if (backMsg.value.row) {
        referQuery.value.projectId = backMsg.value.row.projectId;
        referQuery.value.deptId = String(backMsg.value.row.applyingOrgId);
        referQuery.value.reviewStatus = backMsg.value.val;
        // 转换成 ReviewRecordsVO 类型
        const reviewData = {
          id: 0, // 使用默认值，后端会处理
          projectId: Number(referQuery.value.projectId),
          deptId: Number(referQuery.value.deptId),
          operationUser: '', // 后端会从当前会话获取
          filePath: referQuery.value.filePath || '',
          fileId: 0, // 使用默认值，后端会处理
          reviewStatus: String(referQuery.value.reviewStatus),
          reviewComments: referQuery.value.reviewComments || ''
        };
        ReviewRecordsApi.createReviewRecords(reviewData).then((res) => {
          dialogFormVisible.value = false;
          getList();
          formEl.resetFields();
          ElMessage({ type: 'success', message: backMsg.value.Mg });
        });
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

const submitFormBack2 = () => {
  getUserProfile().then((res) => {
    // 构造符合 ReviewRecordsVO 的对象并包含 projectIds 属性
    const batchData = {
      id: 0,
      projectId: 0,
      deptId: Number(res.dept.id),
      operationUser: '',
      filePath: '',
      fileId: 0,
      reviewStatus: selectMsg.value,
      reviewComments: ruleFormBack.reviewComments,
      projectIds: toRaw(selectArr.value),
    };
    // 使用 @ts-ignore 跳过类型检查，因为后端API支持额外的 projectIds 参数
    // @ts-ignore
    ReviewRecordsApi.createBatch(batchData).then((res) => {
      dialogFormVisible.value = false;
      getList();
    })
  })
}

</script>
<style scoped>
.linet{
  width:100%;
  height:30px;
  font-size: 20px;
  font-weight: bold;
  line-height: 30px;
  /* border-left:4px solid #39559F; */
  border-left:4px solid red;
}
.linet2{
  width:100%;
  height:30px;
  font-size: 20px;
  font-weight: bold;
  line-height: 30px;
  border-left:4px solid #39559F;
  /* border-left:4px solid red; */
}
/* 表格视图 */
.tableTD {
  width: 100%;
  margin: 0 auto;
  /*合并多个单元格边框*/
  border-collapse: collapse;
}
.tableTD th {
  color: black;
  font-size: 15px;
  padding:10px;
  border: 1px solid #c0c4cc;
  background-color: #F2F2F2 !important;
}
.tableTD tr td {
  padding: 10px;
  font-size: 15px;
  background-color: #fff;
  border: 1px solid #c0c4cc;
}
.sty_text{
  font-family: '宋体', SimSun, sans-serif;color:black;
}
.block_tag{
  height:38px;
  display:block;
  min-width:100px;
  line-height:38px;
  text-align:center;
  border-bottom:1px solid #e4e7ed;
}
:deep(.sel_tab .el-select__wrapper) {
  background:#FDF6EC !important;
  border:1px solid #F3D19E !important;
}
:deep(.sel_tab .el-select__placeholder) {
  color:#E6A23C !important;
}
:deep(.sel_tab .el-icon svg) {
  color:#E6A23C !important;
}
:deep(.sel_tab .el-select__wrapper.is-focused) {
  box-shadow: 0 0 0 1px #E6A23C inset;
}
.your-custom-class .el-button + .el-button {
  margin-left: 0 !important;
}
.tag_sty{
  margin-left: 0;
  width:110px;
}
.cz_sty{
  color:#656565;
  font-weight: bold;
}
.scrollbar_pd{
  padding-left:15px;
  padding-right:15px;
}
.red_text{
color:#F56C6C;
}
.red_text:hover{
  color:#D42C2C;
}
.blue_text{
  color:#409EFF;
}
.blue_text:hover{
  color:#0070C0;
}
.green_text{
  color:#67C23A;
}
.green_text:hover{
  color:#16AE43;
}
/* 操作列按钮间距 */
.el-table .el-table__body .el-button + .el-button {
  margin-left: 4px !important;
}
</style>
<style>
/* .el-button--text:not(.is-disabled):hover{
  color: #0070C0;
}  */
.el-button--primary:hover{
  color: #0070C0 !important;
}
.el-button--danger:hover{
  color: #D32222 !important;
}
:deep(.ml_but .el-button+.el-button) {
  margin-left:0px !important;
}
/* popover（操作样式） */
/* .el-popper.is-customized {
  padding: 6px 12px;
  background: linear-gradient(90deg, rgb(159, 229, 151), rgb(204, 229, 129));
}

.el-popper.is-customized .el-popper__arrow::before {
  background: linear-gradient(45deg, #b2e68d, #bce689);
  right: 0;
} */
:deep(.but_br .el-button .el-button--primary) {
  display: block !important;
}
:deep(.el-popper) {
  padding:0px !important;
}
</style>
