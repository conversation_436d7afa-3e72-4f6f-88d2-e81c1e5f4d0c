<template>
  <div class="rich-text-container">
    <el-row>
      <el-col :span="5" style="margin-right:15px">
        <el-card shadow="always" style="height: calc(100vh - 160px);width:100%;" >
          <div class="left_tit">
            <!-- :class="{ active: activeIndex === index }" -->
            <h4 :data-id="item.msg" v-for="(item,index) in leftTit" :key="index+'ew'"  @click="setActive(index),scrollToElement(item.msg)" class="word">{{ item.tit }}</h4>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18">
        <el-card shadow="always" style="width:100%;height: calc(100vh - 160px);margin: auto;">
          <el-card shadow="never"  style="width:93%; height:  calc(100vh - 180px);margin: auto;padding:0;border:0;">
            <div ref="scrollContainer" style="width:100%; height: calc(100vh - 250px);margin-top:10px;margin-bottom:10px;padding-right:10px;overflow: auto;">
              <div id="part1">
                <div  class="mb-8 pl-5 linet" style="margin-top:30px;">项目基本信息表</div>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%" class="tableTD">
                  <tr>
                    <th class="title" colspan="2" width="18%">项目名称</th>
                    <td colspan="5">{{ ruleForm.pmName }}</td>
                  </tr>
                  <tr>
                    <th class="title" colspan="2">验证类别</th>
                    <td colspan="5">
                      <span>{{ ruleForm.validationCategory ? pmValidationCategory.find(item => item.value.toString() === ruleForm.validationCategory).label : '请选择' }}</span>
                    </td>
                  </tr>
                  <tr>
                    <th class="title" colspan="2">技术领域</th>
                    <td colspan="5">{{ ruleForm.academicField }}</td>
                  </tr>
                  <tr>
                    <th class="title" colspan="2">拟转化地域</th>
                    <td colspan="5">
                      <span>{{ ruleForm.conversionRegion ? pmConversionRegion.find(item => item.value.toString() === ruleForm.conversionRegion).label : '请选择' }}</span>
                    </td>
                  </tr>
                  <tr>
                    <th class="title" colspan="2" rowspan="2">经费预算</th>
                    <td colspan="5">总预算 {{ ruleForm.totalBudgetWan }}万元，
                      其中申请专项资金 {{ ruleForm.specialFundApplication }} 万元
                    </td>
                  </tr>
                  <tr>
                    <td colspan="5">单位自筹资金 {{ ruleForm.selfFunding }} 万元，其他渠道获得资金 {{ ruleForm.otherFunding }} 万元</td>
                  </tr>
                  <tr>
                    <th class="title" colspan="2">项目周期节点</th>
                    <th class="title" >起始时间</th>
                    <td style="width:10%;">{{ ruleForm.projectStartDate }}</td>
                    <th class="title" width="8%">结束日期</th>
                    <td colspan="2">{{ruleForm.projectEndDate}}</td>
                  </tr>
                  <tr>
                    <th class="title" rowspan="2" width="8%">申报单位</th>
                    <th class="title" >单位名称</th>
                    <td colspan="5">{{ ruleForm.applyingOrgId }}</td>
                  </tr>
                  <tr>
                    <th class="title" >通信地址</th>
                    <td colspan="2">
                      {{ruleForm.area}}
                    </td>
                    <th class="title" >详细地址</th>
                    <td colspan="2">
                      {{ ruleForm.applyingOrgAddress }}
                    </td>
                  </tr>
                  <tr>
                    <th class="title" rowspan="5">项目负责人</th>
                    <th class="title" >姓名</th>
                    <td> {{ ruleForm.managerName }}</td>
                    <th class="title">性别</th>
                    <td>{{ ruleForm.managerGender==1?'男':'女' }}
                    </td>
                    <th class="title">出生日期</th>
                    <td>{{ruleForm.managerBirthDate}}</td>
                  </tr>
                  <tr>
                    <th class="title" >证件类型</th>
                    <td  width="18%">
                      <span>{{ ruleForm.managerIdType ? pmIdType.find(item => item.value.toString() === ruleForm.managerIdType).label : '请选择' }}</span>
                    </td>
                    <th class="title">证件号码</th>
                    <td colspan="3">{{ ruleForm.managerIdNumber }}</td>
                  </tr>
                  <tr>
                    <th class="title" >最高学位</th>
                    <td colspan="5">
                      <span>{{ ruleForm.managerHighestDegree ? pmHighestDegree.find(item => item.value.toString() === ruleForm.managerHighestDegree).label : '请选择' }}</span>
                    </td>
                  </tr>
                  <tr>
                    <th class="title" >职称</th>
                    <td colspan="2">
                      <span>{{ ruleForm.managerTitle ? pmTitle.find(item => item.value.toString() === ruleForm.managerTitle).label : '请选择' }}</span>
                    </td>
                    <th class="title">职务</th>
                    <td colspan="2">{{ ruleForm.managerPosition }}</td>
                  </tr>
                  <tr>
                    <th class="title" >电子邮箱</th>
                    <td colspan="2">{{ ruleForm.managerEmail }}</td>
                    <th class="title" >移动电话</th>
                    <td colspan="2">{{ ruleForm.managerMobile }}</td>
                  </tr>
                  <tr>
                    <th class="title"  rowspan="3">项目联系人</th>
                    <th class="title" >姓名</th>
                    <td colspan="2">{{ ruleForm.contactName }}</td>
                    <th class="title" >电子邮箱</th>
                    <td colspan="2">{{ ruleForm.contactEmail }}</td>
                  </tr>
                  <tr>
                    <th class="title" >固定电话</th>
                    <td colspan="2">{{ ruleForm.contactPhone }}</td>
                    <th class="title" >移动电话</th>
                    <td colspan="2">{{ ruleForm.contactMobile }}</td>
                  </tr>
                  <tr>
                    <th class="title">证件类型</th>
                    <td colspan="2" >
                      <span>{{ ruleForm.contactIdType ? pmIdType.find(item => item.value.toString() === ruleForm.contactIdType).label : '请选择' }}</span>
                    </td>
                    <th class="title" >证件号码</th>
                    <td colspan="2">{{ ruleForm.contactIdNumber }}</td>
                  </tr>
                  <tr>
                    <th class="title"  :rowspan="(otherjg.length+1).toString()">其他参与单位：</th>
                    <th class="title" >序号</th>
                    <th class="title"  colspan="3">单位名称</th>
                    <th class="title" colspan="2">单位性质</th>
                  </tr>
                  <tr v-for="(item,index) in otherjg" :key="index+'i3'">
                    <td class="title" >{{ index+1 }}</td>
                    <td class="title" colspan="3">{{ item.orgName }}</td>
                    <td class="title" colspan="2">{{ item.orgQuality }}</td>
                  </tr>
                  <tr>
                    <th class="title" colspan="2" rowspan="2">项目参加人数</th>
                    <td rowspan="2" >{{ ruleForm.totalParticipants }}人。其中：
                    </td>
                    <td colspan="4">
                      高级职称 {{ ruleForm.seniorTitleCount }} 人，
                      中级职称 {{ ruleForm.mediumTitleCount }} 人，
                      初级职称 {{ ruleForm.juniorTitleCount }} 人，
                      其他 {{ ruleForm.otherTitleCount }} 人；
                    </td>
                  </tr>
                  <tr>
                    <td colspan="4">
                      博士学位 {{ ruleForm.doctorDegreeCount }} 人，
                      硕士学位 {{ ruleForm.masterDegreeCount }} 人，
                      学士学位 {{ ruleForm.bachelorDegreeCount }} 人，
                      其他 {{ ruleForm.otherDegreeCount }} 人；
                    </td>
                  </tr>
                </table>
              </div>
              <div id="part2" class="rich-text-container">
                <div  class="mb-8 mt-15 pl-5 linet">项目简介</div>
                <h4 class="mb-1" style="text-indent: 2em;color:#727272;">{{placeholderMsg}}</h4>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%" class="tableTD">
                  <tr>
                    <td style="height:280px;text-align: left;">{{ ruleForm2.projOverview }}</td>
                  </tr>
                </table>
              </div>
              <div id="part3" class="rich-text-container" >
                <div  class="mb-8 mt-15 pl-5 linet">项目基本情况介绍</div>
                <h4 class="mb-1" style="text-indent: 2em;color:#727272;">{{placeholderMsg2}}</h4>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%" class="tableTD">
                  <tr>
                    <td>
                      <div style="min-height:300px;text-align: left;" v-html="ruleForm3.projIntroductions"></div>
                    </td>
                  </tr>
                </table>
              </div>
              <div id="part4" class="rich-text-container">
                <div class="mb-8 mt-15 pl-5 linet">项目预期目标与产业化规划</div>
                <h4 class="mb-1" style="text-indent: 2em;color:#727272;line-height: -1;">{{placeholderMsg3}}</h4>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%" class="tableTD">
                  <!-- <tr>
                    <td style="padding:15px;">{{}}</td>
                  </tr> -->
                  <tr>
                    <td><div style="min-height:300px;" v-html="ruleForm4.projGoalsPlan"></div></td>
                  </tr>
                </table>
              </div>
              <div id="part5" class="rich-text-container">
                <div  class="mb-8 mt-15 pl-5 linet">项目成熟度自我评判</div>
                <h4 class="mb-1" style="text-indent: 2em;color:#727272;">{{placeholderMsg4}}</h4>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%" class="tableTD">
                  <!-- <tr>
                    <td  style="padding:15px;">{{placeholderMsg4}}</td>
                  </tr> -->
                  <tr>
                    <td>
                      <div style="min-height:300px;" v-html="ruleForm5.projMaturityAssessment"></div>
                    </td>
                  </tr>
                </table>
              </div>
              <div id="part6" class="rich-text-container">
                <div  class="mb-8 mt-15 pl-5 linet">项目支持</div>
                <h4 class="mb-1" style="text-indent: 2em;color:#727272;">{{placeholderMsg5}}</h4>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%" class="tableTD">
                  <tr>
                    <td><div style="min-height:300px;">{{ ruleForm6.projSupport }}</div></td>
                  </tr>
                </table>
              </div>
              <div id="part7" class="rich-text-container">
                <div class="mb-8 mt-15 pl-5 linet">项目实施方案</div>
                <h4 class="mb-1" style="text-indent: 2em;color:#727272;">{{placeholderMsg6}}</h4>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%" class="tableTD">
                  <tr>
                    <td><div style="min-height:300px;" v-html="ruleForm7.projImplPlan"></div></td>
                  </tr>
                </table>
              </div>
              <div id="part8" >
                <div class="mb-8 mt-15 pl-5 linet">项目目标及考核指标</div>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%" class="tableTD">
                  <tr>
                    <th class="title" colspan="2" >项目名称</th>
                    <td colspan="5">{{ruleForm.pmName}}</td>
                  </tr>
                  <tr>
                    <th class="title" colspan="2" >单位名称</th>
                    <td colspan="5">{{ ruleForm.applyingOrgName }}</td>
                  </tr>
                  <tr>
                    <th class="title" rowspan="4">项目资金（万元）</th>
                    <th>资金总额</th>
                    <td colspan="5">{{ruleForm.totalBudgetWan}}（万元）</td>
                  </tr>
                  <tr>
                    <th>其中：申请专项资金</th>
                    <td colspan="5">{{ruleForm.specialFundApplication}}（万元）</td>
                  </tr>
                  <tr>
                    <th>单位自筹资金</th>
                    <td colspan="5">{{ruleForm.selfFunding}}（万元）</td>
                  </tr>
                  <tr>
                    <th>其他渠道获得资金</th>
                    <td colspan="5">{{ruleForm.otherFunding}}（万元）</td>
                  </tr>
                  <tr>
                    <th rowspan="2">绩效目标</th>
                    <td colspan="6" style="color:#727272;">
                      <b>根据项目特点撰写（建议：原理验证</b>重点目标是验证技术的可实现性；
                      <b>产品与场景体系验证</b>重点验证产品实现的基础条件及应用场景体系实现过程中关键技术、
                      加工测试工艺、系统集成验证等技术手段以及产业支持政策体系的可实现性；
                      <b>原型制备与技术可行性验证</b>验证成果产业化的技术可行性及需要的基础条件，
                      完成符合设计要求的小批量试制工艺技术体系验证，明确工程化过程中的技术风险；
                      <b>商业前景验证</b>需形成推广应用的政策需求分析报告和市场竞争策略报告等；
                      <b>其他类应</b>通过项目调研、创业咨询辅导、小批量试制、二次开发、中试熟化等产创融
                      合链接服务实现投资转化<b>）</b>。
                    </td>
                  </tr>
                  <tr>
                    <td colspan="7"><div style="min-height:300px;">{{ ruleFormT8.performanceTarget }}</div></td>
                  </tr>

                </table>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%;margin-top:10px;" class="tableTD">
                  <tr>
                    <th >一级指标</th>
                    <th  width="23%">二级指标</th>
                    <th width="28%">三级指标</th>
                    <th width="25%">考核方式方法</th>
                    <th>指标分</th>
                  </tr>
                  <tr v-for='(itemOne,index) in treeData' :key='index+"o"'>
                    <th>{{ itemOne.label }}</th>
                    <td colspan="6" valign="top" style='padding:0px;'>
                      <table border cellspacing="0" cellpadding="0" style="width:100%;" class="tableTD2 ">
                        <tr  v-for='(itemTwo,index) in itemOne.children' :key="index+'w'">
                          <th width='25.25%'>
                            <span>{{itemTwo.label }}</span>
                          </th>
                          <td style='padding:0px;' >
                            <table border cellspacing="0" cellpadding="0" style="width:100%;" class="tableTD2 ">
                              <tr v-for='(arritem, index) in arrChildren' :key="index+'ic'" v-show="(arritem.parentId==itemTwo.value)">
                                <td width='41.4%' style="padding:0px;border-left:0;" align="center">{{arritem.indicatorName}}</td>
                                <td width='36.9%' style="padding:0px;border-left:0;" align="center">
                                  {{ arritem.assessmentMethod ? pmAssessmentMethod.find(item => item.value.toString() === arritem.assessmentMethod).label : '请选择' }}
                                  <!-- <el-form-item prop="assessmentMethod">
                                    <el-select v-model="arritem.assessmentMethod" placeholder="请选择考核方式">
                                      <el-option v-for="item_mentod in pmAssessmentMethod" :key="item_mentod.value" :label="item_mentod.label" :value="item_mentod.value.toString()" />
                                    </el-select>
                                  </el-form-item> -->
                                </td>
                                <td align="center" style="padding:0px;border-left:0;">{{arritem.indicatorScore}}</td>
                              </tr>
                            </table>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                </table>
              </div>
              <div id="part9" >
                <div class="mb-8 mt-15 pl-5 linet">项目团队基本情况表</div>
                <div style="width:100%;overflow: auto;min-height: 150px;border:1px solid black;">
                  <table class="fixtable tableTD" border="1"  cellspacing="0" cellpadding="0" style="width:200%;border-top:1px solid #fff;">
                    <tr>
                      <th width="100px">姓名</th>
                      <th width="60px">性别</th>
                      <th width="110px">出生年月</th>
                      <th width="180px">身份证号码</th>
                      <th width="230px">专业技术职称</th>
                      <th width="130px">职务</th>
                      <th width="150px">最高学位</th>
                      <th width="230px">专业</th>
                      <th width="100px">投入本项目的全时工作时间</th>
                      <th width="150px">工作单位</th>
                      <th width="230px">承担的主要工作</th>
                    </tr>
                    <tr  v-for="(item,index) in tableData" :key="index+'o9'">
                      <td align="center">{{ item.memberName }}</td>
                      <td align="center">{{ gender.find(t => t.value.toString() === item.gender)?.label || item.gender}}</td>
                      <td align="center">{{ item.birthdate }}</td>
                      <td align="center">{{ item.idCardNumber }}</td>
                      <td align="center">{{ pmTitle.find(t => t.value.toString() === item.technicalTitle)?.label || item.technicalTitle }}</td>
                      <td align="center">{{ item.position }}</td>
                      <td align="center">{{ pmHighestDegree.find(d => d.value.toString() === item.highestDegree)?.label || item.highestDegree }}</td>
                      <td align="center">{{ item.major }}</td>
                      <td align="center">{{ item.fullTimeHoursPerMonth }}</td>
                      <td align="center">{{ item.workUnit }}</td>
                      <td align="center">{{ item.mainResponsibilities }}</td>
                    </tr>
                  </table>
                </div>
              </div>
              <div id="part10">
                <div class="mb-8 mt-15 pl-5 linet">项目经费支出</div>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%" class="tableTD">
                </table>
                <table border="1"  cellspacing="0" cellpadding="0" style="width:100%" class="tableTD">
                  <tr>
                    <th class="h-12" width="5%">序号</th>
                    <th width="18%">支出项目</th>
                    <th colspan="4" width="50%">支出金额（单位：万元）</th>
                    <th rowspan="2">测算依据及说明</th>
                  </tr>
                  <tr>
                    <th class="h-10">（一）</th>
                    <th>直接费用</th>
                    <th>申请专项资金</th>
                    <th>单位自筹资金</th>
                    <th>其他渠道获得资金</th>
                    <th>合计</th>
                    <!-- <th></th> -->
                  </tr>
                  <tr>
                    <td>1</td>
                    <td>设备费</td>
                    <td>{{ruleForm10.equipmentSpecial}}</td>
                    <td>{{ ruleForm10.equipmentSelf }}</td>
                    <td>{{ ruleForm10.equipmentOther }}</td>
                    <td>{{ ruleForm10.equipmentTotal }}</td>
                    <td>{{ ruleForm10.equipmentBasisNotes }}</td>
                  </tr>
                  <tr>
                    <td>2</td>
                    <td>业务费</td>
                    <td>{{ ruleForm10.businessSpecial }}</td>
                    <td>{{ ruleForm10.businessSelf }}</td>
                    <td>{{ruleForm10.businessOther}}</td>
                    <td>{{ ruleForm10.businessTotal }}</td>
                    <td>{{ ruleForm10.businessBasisNotes }}</td>
                  </tr>
                  <tr>
                    <td>3</td>
                    <td>劳务费</td>
                    <td>{{ ruleForm10.laborSpecial }}</td>
                    <td>{{ ruleForm10.laborSelf }}</td>
                    <td>{{ ruleForm10.laborOther }}</td>
                    <td>{{ ruleForm10.laborTotal }}</td>
                    <td>{{ ruleForm10.laborBasisNotes }}</td>
                  </tr>
                  <tr>
                    <th class="h-10">（二）</th>
                    <th>间接费用</th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                    <th></th>
                  </tr>
                  <tr>
                    <td>1</td>
                    <td>间接费用</td>
                    <td>{{ ruleForm10.indirectSpecial }}</td>
                    <td>{{ ruleForm10.indirectSelf }}</td>
                    <td>{{ ruleForm10.indirectOther }}</td>
                    <td>{{ ruleForm10.indirectTotal }}</td>
                    <td>{{ ruleForm10.indirectBasisNotes }}</td>
                  </tr>
                  <tr>
                    <th colspan="2">合计</th>
                    <td>{{ruleForm10.specialTotal}}</td>
                    <td>{{ruleForm10.specialSelf}}</td>
                    <td>{{ruleForm10.specialOther}}</td>
                    <td>{{ ruleForm10.totalFunding }}</td>
                  </tr>
                  <tr>
                    <td colspan="7" style="text-align:left;">
                      <h4>一、项目支出范围</h4>
                      <p>
                        （一）直接费用包括：<br />
                        设备费：在项目实施过程中购置或试制专用仪器设备，对现有仪器设备进行升级改造，以及租赁外单位仪器设备而发生的费用。计算类仪器设备和软件工具可在设备费科目列支。应当严格控制设备购置，鼓励开放共享、自主研制、租赁专用仪器设备以及对现有仪器设备进行升级改造，避免重复购置。<br />
                        业务费：在项目实施过程中消耗的各种材料、辅助材料等低值易耗品的采购、运输、装卸、整理等费用，发生的测试化验加工、燃料动力、出版/文献/信息传播/知识产权事务、会议/差旅/国际合作交流等费用，以及其他相关支出。<br />
                        劳务费：在项目实施过程中支付给参与项目的研究生、博士后、访问学者和项目聘用的研究人员、科研辅助人员等的劳务性费用，以及支付给临时聘请的咨询专家的费用等。<br />
                      </p>
                      <p>
                        （二）间接费用是指承担单位在组织实施项目过程中发生的无法在直接费用中列支的相关费用。主要包括：承担单位为项目研究提供的房屋占用，日常水、电、气、暖等消耗，有关管理费用的补助支出等。
                      </p>
                    </td>
                  </tr>
                </table>
              </div>
            </div>
          </el-card>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script setup lang="ts">
import { ref,onMounted,onBeforeUnmount } from 'vue';
// 1 引入useRouter路由信息方法
import { useRoute } from 'vue-router'
// 2 获取实例
const route = useRoute();
const { id } = route.query;
import { defineComponent, reactive } from 'vue';
import { ProjBasicApi } from '@/api/pm/projbasic'
import { SubjectInfoApi } from '@/api/pm/subjectinfo'
import type { ComponentSize, } from 'element-plus'
import { getStrDictOptions,  } from '@/utils/dict'//获取字典
/** 成果概念验证项目基本信息 表单 */
defineOptions({ name: 'ProjBasicForm' })

const pmValidationCategory = ref(getStrDictOptions(DICT_TYPE.PM_VALIDATION_CATEGORY));//验证类别
const pmConversionRegion = ref(getStrDictOptions(DICT_TYPE.PM_CONVERSION_REGION));//拟转化地域
const pmIdType = ref(getStrDictOptions(DICT_TYPE.PM_ID_TYPE));//证件类型
const pmHighestDegree = ref(getStrDictOptions(DICT_TYPE.PM_HIGHEST_DEGREE));//最高学位
const pmTitle = ref(getStrDictOptions(DICT_TYPE.PM_TITLE));//职称
const pmAssessmentMethod = ref(getStrDictOptions(DICT_TYPE.PM_ASSESSMENT_METHOD));//职称
const gender = ref(getStrDictOptions(DICT_TYPE.SYSTEM_USER_SEX));//职称
const activeIndex=ref(null) // 当前激活的词的索引
const leftTit = ref([
  {tit:'项目基本信息表',msg:'part1'},
  {tit:'一、项目简介',msg:'part2'},
  {tit:'二、项目基本情况介绍',msg:'part3'},
  {tit:'三、项目预期目标与产业化规划',msg:'part4'},
  {tit:'四、项目成熟度自我评判',msg:'part5'},
  {tit:'五、项目支持',msg:'part6'},
  {tit:'六、项目实施方案',msg:'part7'},
  {tit:'七、项目目标及考核指标',msg:'part8'},
  {tit:'八、项目团队基本情况表',msg:'part9'},
  {tit:'九、项目经费支出',msg:'part10'},
])
const scrollContainer = ref<HTMLElement | null>(null); // 引用滚动容器

const labelRowCenter = ref({
  "borderColor":'red'
})
const setActive=(index)=> {
  activeIndex.value = index; // 设置当前激活的词的索引
}
const scrollToElement = (id) => {
  const section  = document.getElementById(id);
  if (section) {
    // 使用 scrollIntoView 滚动到目标元素
    section.scrollIntoView({ behavior: 'smooth', });
  }
};
const highlightSection = () => {
  const scrollPosition = scrollContainer.value?.scrollTop || 0; // 使用容器的 scrollTop
  leftTit.value.forEach(section => {
    const element = document.getElementById(section.msg);
    const navItem = document.querySelector(`div h4[data-id="${section.msg}"]`);
    if (element) {
      const { top, bottom } = element.getBoundingClientRect();
      // console.log(scrollContainer.value!.scrollTop,'00pppp')
      const elementTop = top + scrollContainer.value!.scrollTop-200; // 计算元素的绝对位置
      const elementBottom = bottom +  scrollContainer.value!.scrollTop-150;
      if (scrollPosition >= elementTop && scrollPosition < elementBottom) {
        navItem?.classList.add('active');
      } else {
        navItem?.classList.remove('active');
      }
    }
  });
};

// 学科数据
const ttttvData=ref()
SubjectInfoApi.getTreeSubjectInfo().then((res) => {
  ttttvData.value = res;
})
//机构树
const options = ref([]);
ProjBasicApi.getAreaTree().then((res) => {
  options.value=res;
})



const treeData=ref([]);//7.三级指标树
const arrChildren = ref([]);

const getTreedataFun=()=>{
  ProjBasicApi.tree({ projectId: id }).then((res) => {
    treeData.value = res;
    arrChildren.value = []; // 清空现有的 arrChildren，防止旧数据干扰
    treeData.value.forEach(itemOne => {
      itemOne.children.forEach(itemTwo => {
        if (itemTwo.children) { // 确保有第三级指标
          itemTwo.children.forEach(itemThree => {
            // 假设 itemThree 有以下属性：value, sort, indicatorName, assessmentMethod, indicatorScore
            arrChildren.value.push({
              value: itemThree.value, // 根据实际属性调整
              sort: itemThree.sort,
              indicatorName: itemThree.label, // 指标名称
              assessmentMethod: itemThree.assessmentMethod, // 考核方法
              indicatorScore: itemThree.indicatorScore, // 指标分数
              indicatorLevel: itemThree.indicatorLevel, // 指标分数
              parentId: itemThree.parentId, // 指标分数
              projectId: itemThree.projectId, // 指标分数
              id: itemThree.value, // 指标分数
            });
          });
        }
      });
    });
  })
}







interface InputItem {
  xh: string;
  dwmc: string;
  dwxz: string;
}
const inputList = reactive<InputItem[]>([{ xh: '', dwmc: '', dwxz: '' }]);

//验证1
interface RuleForm {
  province:string
  city:string
  county:string
  area: string
  academicField:string
  pmName: string
  applyingOrgName:string
  allOrgAddress:string[]
  validationCategory: string
  pmType: string
  discipline:string[]
  conversionRegion: string
  totalBudgetWan: string
  specialFundApplication: string
  selfFunding: string
  otherFunding: string
  projectStartDate:string
  projectEndDate:string
  dwmc: string
  applyingOrgAddress: string
  applyingOrgId:string
  managerName: string
  applyingDate:string
  managerGender: string
  managerBirthDate: string
  managerIdType: string
  managerIdNumber: string
  managerHighestDegree: string
  managerTitle:string
  managerPosition: string
  managerEmail: string
  managerMobile: string
  contactName: string
  contactEmail: string
  contactPhone: string
  contactMobile: string
  contactIdType: string
  contactIdNumber: string
  totalParticipants: string
  seniorTitleCount: string
  mediumTitleCount: string
  juniorTitleCount: string
  otherTitleCount: string
  doctorDegreeCount: string
  masterDegreeCount: string
  bachelorDegreeCount: string
  otherDegreeCount: string
}
const formSize = ref<ComponentSize>('default')
const ruleFormRef = ref(null);
const ruleForm = reactive<RuleForm>({
  pmName: '',
  allOrgAddress:[],
  province:'',
  city:'',
  county: '',
  area: '',
  academicField: '',
  applyingOrgName:'',
  validationCategory: '',
  pmType: '',
  discipline:[],
  conversionRegion: '',
  totalBudgetWan: '',
  specialFundApplication: '',
  selfFunding: '',
  otherFunding: '',
  projectStartDate: '',
  projectEndDate: '',
  dwmc: '',
  applyingOrgAddress: '',
  applyingOrgId:'',
  managerName: '',
  applyingDate:'',
  managerGender: '',
  managerBirthDate: '',
  managerIdType: '',
  managerIdNumber: '',
  managerHighestDegree: '',
  managerTitle:'',
  managerPosition: '',
  managerEmail: '',
  managerMobile: '',
  contactName: '',
  contactEmail: '',
  contactPhone: '',
  contactMobile: '',
  contactIdType: '',
  contactIdNumber: '',
  totalParticipants: '',
  seniorTitleCount: '',
  mediumTitleCount: '',
  juniorTitleCount: '',
  otherTitleCount: '',
  doctorDegreeCount: '',
  masterDegreeCount: '',
  bachelorDegreeCount: '',
  otherDegreeCount: '',
})

const formRules = reactive({
})

//验证2
interface RuleForm2{
  projectId:string,
  projOverview: string,
}
const placeholderMsg=ref("概述本科技成果的创新概念及研究基础，通过科技创新所形成的核心技术及拟解决的关键问题，应用场景，对经济社会发展的推动作用，项目技术资金需求等（限500字以内）。")
const ruleFormRef2 = ref(null);
const ruleForm2 = reactive<RuleForm2>({
  projectId:'',
  projOverview: "",
})
//验证3
interface RuleForm3{
  projIntroductions:string,
}
const placeholderMsg2 = ref('概述本科技成果创新点及核心竞争力；现有技术基础、知识产权背景；拟进一步解决的关键科学技术问题；拟解决的产业难点与痛点及其可能产生的市场影响（限2000字以内）。');
const ruleFormRef3 = ref(null);
const ruleForm3 = reactive<RuleForm3>({
  projIntroductions:'<p>概述本科技成果创新点及核心竞争力（请在下方填写）：<br /><br /><br />现有技术基础、知识产权背景（请在下方填写）：<br /><br /><br />拟进一步解决的关键科学技术问题（请在下方填写）：<br /><br /><br />拟解决的产业难点与痛点及其可能产生的市场影响（请在下方填写）：<br /><br /><br /></p>',
})

//验证4
interface RuleForm4{
  projGoalsPlan:string,
}
const placeholderMsg3 = ref('描述本项目科技成果实施概念验证后的预期目标与产业化规划，主要突出以下五项内容：未来成果转化地域和方式；未来形成的产品和目标客户；市场需求规模及本项目形成产品未来的市场空间；结合未来 3-5 年市场前景预测，描述未来产业规划，包括如何调整技术/产品/服务来迅速占领、拓展市场；根据项目概念验证的时间安排和关键节点，描述未来两年拟完成的产业化规划与目标、关键节点/里程碑（限2000字以内）。');
const ruleFormRef4 = ref(null);
const ruleForm4 = reactive<RuleForm4>({
  projGoalsPlan:'',
})
//验证4
interface RuleForm5{
  projMaturityAssessment:string,
}
const placeholderMsg4 = ref('根据国家工信部颁布的《技术成熟度等级划分及定义》（见附表），对项目成熟度进行自我评判（限500字以内）。');
const ruleFormRef5 = ref(null);
const ruleForm5 = reactive<RuleForm5>({
  projMaturityAssessment:'',
})
//验证4
interface RuleForm6{
  projSupport:string,
}
const placeholderMsg5 = ref('简要介绍项目团队、资金等已具备的条件；项目所需支持的条件，包括资金、场地、产业资源、其他人员配备等（限1000字以内）。');
const ruleFormRef6 = ref(null);
const ruleForm6 = reactive<RuleForm6>({
  projSupport:'',
})
//验证4
interface RuleForm7{
  projImplPlan:string,
}
const placeholderMsg6 = ref('主要阐述本项目需在哪些方面需进一步验证才能走向产业化，项目潜在风险点及其解决方案；本项目科技成果概念验证的具体内容、实施方案（限3000字以内）。');
const ruleFormRef7 = ref(null);
const ruleForm7 = reactive<RuleForm7>({
  projImplPlan:'',
})

//验证4
interface RuleT8{
  performanceTarget:string,
}
const ruleFormT8 = reactive<RuleT8>({
  performanceTarget:'',
})

//验证8
interface RuleForm8{
  projImplPlan:string,
}
const ruleFormRef8 = ref(null);
const ruleForm8 = reactive<RuleForm8>({
  projImplPlan:'',
})


interface RuleForm9{
  projectId:string,
  memberName:string,
  gender:string,
  birthdate:string,
  idCardNumber:string,
  technicalTitle:string,
  position:string,
  highestDegree:string,
  major:string,
  fullTimeHoursPerMonth:string,
  workUnit:string,
  mainResponsibilities:string,
}
const ruleFormRef9 = ref(null)
const ruleForm9 = reactive<RuleForm9>({
  projectId:"",
  memberName:"",
  gender:"",
  birthdate:"",
  idCardNumber:"",
  technicalTitle:"",
  position:"",
  highestDegree:"",
  major:"",
  fullTimeHoursPerMonth:"",
  workUnit:"",
  mainResponsibilities:"",
})




interface RuleForm10{
  equipmentTotal:string,
  businessTotal:string,
  laborTotal:string,
  indirectTotal:string,
  equipmentSpecial:string,
  equipmentSelf:string,
  equipmentOther:string,
  equipmentBasisNotes: string,
  businessSpecial:string,
  businessSelf:string,
  businessOther:string,
  businessBasisNotes: string,
  laborSpecial:string,
  laborSelf:string,
  laborOther:string,
  laborBasisNotes: string,
  indirectSpecial:string,
  indirectSelf:string,
  indirectOther:string,
  indirectBasisNotes:string,
  specialTotal:string,
  specialSelf:string,
  specialOther:string,
  totalFunding:string,
}
const ruleFormRef10 = ref(null);
const ruleForm10 = reactive<RuleForm10>({
  equipmentTotal:'',
  businessTotal:'',
  laborTotal:'',
  indirectTotal:'',
  equipmentSpecial:'',
  equipmentSelf:'',
  equipmentOther:'',
  equipmentBasisNotes: '',
  businessSpecial:'',
  businessSelf:'',
  businessOther:'',
  businessBasisNotes: '',
  laborSpecial:'',
  laborSelf:'',
  laborOther:'',
  laborBasisNotes: '',
  indirectSpecial:'',
  indirectSelf:'',
  indirectOther:'',
  indirectBasisNotes:'',
  specialTotal:'',
  specialSelf:'',
  specialOther:'',
  totalFunding:'',
})

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
})
const tableData = ref([
  {
    memberName: '', gender: '', birthdate: '', idCardNumber: '',
    technicalTitle: '', position: '', highestDegree: '', major: '', fullTimeHoursPerMonth: '', workUnit: '', mainResponsibilities: ''
  }

])//第8
const newFormData = ref([]);
const newFormData2 = ref();
const queryParamsUserLIst = reactive({
  pageNo:1,
  pageSize: 10,
  projectId:'',
})
const getallLst = async (formData,Api,e) => {
  newFormData.value = [];
  if (e === '1' || e === '9') {
    if (e === '9') {
      tableData.value=[]
      queryParamsUserLIst.projectId = id;
      await Api(queryParamsUserLIst).then((res)=>{
        tableData.value = res.list;
      })
    }else{
      newFormData.value = await Api(id)
      for (const key in formData) {
        if (key == 'allOrgAddress') {
          formData[key].push(Number(newFormData.value.province), Number(newFormData.value.city), Number(newFormData.value.county));
        }
        else if (key == 'discipline') {
          newFormData.value.academicFieldOne == "" ? '' : ruleForm.discipline.push(Number(newFormData.value.academicFieldOne));
          newFormData.value.academicFieldTwo == "" ? '' : ruleForm.discipline.push(Number(newFormData.value.academicFieldTwo));
          newFormData.value.academicFieldThree == "" ? '' : ruleForm.discipline.push(Number(newFormData.value.academicFieldThree));
        }
        if (newFormData.value.hasOwnProperty(key)) {
          formData[key] = newFormData.value[key];
        }
      };
    }
  } else if(e==='1_1') {
    await Api({ projectId: id }).then(res => {
      formData.value = res.list;
    });
  } else if (e==='10'||e==='2'||e==='3'||e==='4'||e==='5'||e==='6'||e==='7'||e==='8_1') {
    await Api({ projectId: id }).then(res => {
      newFormData2.value = res;
    });
    for (const key in formData) {
      if (newFormData2.value.hasOwnProperty(key)) {
        formData[key] = newFormData2.value[key];
      }
    };
  }else {
    await Api({ projectId: id }).then(res => {
      newFormData2.value = res.list;
    });
    for (const key in formData) {
      if (newFormData2.value.hasOwnProperty(key)) {
        formData[key] = newFormData2.value[key];
      }
    };
  }

}

const otherjg = ref([]);
const getList =  () => {
  getallLst(ruleForm,ProjBasicApi.getProjBasic,'1');
  getallLst(otherjg,ProjBasicApi.getOtherOrg,'1_1');
  getallLst(ruleForm2,ProjBasicApi.getOverview,'2');
  getallLst(ruleForm3,ProjBasicApi.getIntroductions,'3');
  getallLst(ruleForm4,ProjBasicApi.getGoalsPlan,'4');
  getallLst(ruleForm5,ProjBasicApi.getMaturityAssessment,'5');
  getallLst(ruleForm6,ProjBasicApi.getSupport,'6');
  getallLst(ruleForm7, ProjBasicApi.getImplPlan, '7');
  getallLst(ruleFormT8, ProjBasicApi.getGoalsPage, '8_1');
  getTreedataFun();
  getallLst(ruleForm8,ProjBasicApi.getAssessmentCriteria,'8');
  getallLst(tableData,ProjBasicApi.teamInfopage,'9');
  getallLst(ruleForm10,ProjBasicApi.getBudgetExpenses,'10');

}

/** 初始化 **************************/
onMounted(() => {
  getList();
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', highlightSection); // 绑定到容器
  }
})
onBeforeUnmount(() => {
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', highlightSection); // 移除事件监听器
  }
});
</script>
<style scoped>
.rich-text-container {
  width: 100%; /* 确保外部 div 宽度为 100% */
}
:deep(.rich-text-container img ) {
  max-width: 100%; /* 图片宽度不超过父容器宽度 */
  height: auto; /* 高度自动调整以保持宽高比 */
  object-fit: contain; /* 或者使用 cover */
}
:deep(.el-step__title) {
  font-size: 15px ;
  margin-top:10px;
  line-height: 23px;
}
.tableTD {
  margin-top: 10px;
  border: 1px solid #666;
  margin: 0 auto;
  /*合并多个单元格边框*/
  border-collapse: collapse;
}
.tableTD td{
  padding:7px;
}
.tableTD .title{
  text-align: center;
  padding:6px;
}
.tableTD2 {
  border-width:0px;
  border-style:hidden;
  border-collapse: collapse;
}
.cell-item {
  display: flex;
  align-items: center;
}
.margin-top {
  margin-top: 20px;
}
.pad_tab{
  font-size: 18px;;
  padding:15px !important;
}
.custom-border {
  border-color: #ff0000 !important; /* 红色边框 */
}
.linet{
  width:100%;
  height:30px;
  font-size: 20px;
  font-weight: bold;
  line-height: 30px;
  border-left:4px solid #39559F;

}
</style>
<style>
.custom-border {
  border-color: #ff0000 !important; /* 红色边框 */
}
::-webkit-scrollbar{
  width: 4px;
  height: 6px;
  margin-left:10px;
}
::-webkit-scrollbar-corner{
  display: block;
}
::-webkit-scrollbar-thumb{
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.2);
}
::-webkit-scrollbar-thumb,
::-webkit-scrollbar-track{
  border-right-color: transparent;
  border-left-color: transparent;
  background-color: rgba(0, 0, 0, 0.1);
}</style>


<style>

.fixtable>tr>th:first-child,.fixtable>tr>td:first-child{
  background-color: #ccc;
  position: sticky;
  left: 0;
  z-index: 1;

}

/* 表头固定 */
.fixtable>tr:first-child td {
  position: sticky;
  top: 0;
  z-index: 2;
}
/* 表头首列强制最顶层 */
.fixtable>tr:first-child>td:first-child {
  z-index: 3;
}
.left_tit>h4{
  margin-bottom:20px;
}
.left_tit>h4:hover{
  color:#409EFF;
}
.section {
  height: 500px; /* 设置高度以便于滚动 */
}
.word {
  padding: 5px 8px;
  cursor: pointer;
  /* border: 1px solid #ccc; */
  border-radius: 5px;
  transition: background-color 0.3s;
}
.word.active {
  background-color: #007bff; /* 激活状态的背景颜色 */
  color: white !important; /* 激活状态的文字颜色 */
}
/* .active_scroll{
  font-weight: bold;
  color: red;
} */
</style>
